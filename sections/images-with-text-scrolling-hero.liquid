{%- render 'section-spacing-collapsing' -%}

<style>
  images-with-text-scrolling.images-scrolling-mobile.scroll-area.bleed.sm\:unbleed {
    padding-left: 0px !important;
    padding-right: 0px !important;
    scroll-padding: var(--spacing-4);
    scroll-snap-type: x mandatory;
  }

  .images-scrolling__content {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .section.section--narrow.section-blends.section-full {
    padding-top: 0px !important;
  }

  /* Reduce spacing between this section and the next one */
  @media screen and (max-width: 740px) {
    #shopify-section-{{ section.id }} {
      margin-bottom: -20px !important;
    }
  }

  #shopify-section-{{ section.id }} {
    --images-scrolling-block-count: {{ section.blocks.size }};
    --images-scrolling-image-ratio: {{ section.blocks.first.settings.image.aspect_ratio | default: 1 }};
  }

  /* Floating star decoration styles */
  .images-scrolling__content {
    position: relative;
  }

  {% if section.settings.show_floating_star %}
  .images-scrolling__content::after {
    content: "";
    position: absolute;
    top: {{ section.settings.star_mobile_top_position }}px;
    right: {{ section.settings.star_mobile_right_position }}px;
    width: {{ section.settings.star_size }}px;
    height: {{ section.settings.star_size }}px;
    background: rgb(var(--text-color));
    mask-image: url("{{ section.settings.star_icon | default: 'icon-logo-star.svg' | asset_url }}");
    -webkit-mask-image: url("{{ section.settings.star_icon | default: 'icon-logo-star.svg' | asset_url }}");
    mask-size: contain;
    -webkit-mask-size: contain;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    animation: rotateStar 8s linear infinite;
    opacity: {{ section.settings.star_opacity }};
  }
  {% endif %}

  @keyframes rotateStar {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @media screen and (max-width: 740px) {
    #shopify-section-{{ section.id }} {
      --images-scrolling-grid: {% if section.settings.stack_on_mobile %}none{% else %}auto / auto-flow 73vw{% endif %};
    }
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      --images-scrolling-grid-template-columns: {% if section.settings.image_position == 'start' %}[media] minmax(0, 1.8fr) [content] minmax(0, 1fr){% else %}[content] minmax(0, 1fr) [media] minmax(0, 1.8fr){% endif %};
    }

    .images-scrolling__content::after {
      width: 120px;
      height: 120px;
      background-size: 120px;
      top: {{ section.settings.star_desktop_top_position }}px;
      right: {{ section.settings.star_desktop_right_position }}px;
    }
  }

  .star-rating {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
  }

  .star-rating__wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
  }

  .star-rating__stars-wrapper {
    display: flex;
    align-items: center;
  }

  .star-rating__stars {
    display: flex;
    gap: 4px;
    line-height: 0;
  }

  .star-rating__text-wrapper {
    display: flex;
    align-items: center;
    height: 24px;
  }

  .star-rating__text {
    font-size: 1.1em;
    font-weight: 400;
    line-height: 1;
    display: flex;
    align-items: center;
    font-family: 'FormaDJRText';
  }

  @media screen and (min-width: 741px) {
    .star-rating {
      display: flex;
      align-items: center;
    }

    .star-rating__wrapper {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
    }

    .star-rating__stars-wrapper,
    .star-rating__stars,
    .star-rating__text-wrapper,
    .star-rating__text {
      display: flex;
      align-items: center;
    }

    .star-rating__stars svg {
      display: block;
    }

    .star-rating__text {
      position: relative;
      top: -4px; /* Further adjusted to move text up more */
      line-height: 1;
    }

    /* Ensure vertical centering of whole rating component */
    .star-rating__wrapper {
      position: relative;
      display: inline-flex;
    }
  }

  @media screen and (min-width: 741px) {
    .mobile-full-width {
      display: inline-block;
    }
  }

  @media screen and (max-width: 740px) {
    .mobile-full-width {
      margin: 0 -20px;
      padding: 0 20px;
      width: 100vw;
    }
  }

  /* Mobile spacing improvements */
  @media(max-width: 740px){
    .images-scrolling-mobile__item {
      padding-bottom: var(--spacing-4);
      display: flex;
      flex-direction: column;
    }

    .images-scrolling-mobile__item img {
      width: 100%;
      object-fit: contain;
      max-height: 60vh; /* Use viewport height for better scaling */
      min-height: 350px; /* Ensure minimum size */
      margin-bottom: var(--spacing-4);
    }

    .images-scrolling__content {
      padding-top: var(--spacing-5);
      padding-bottom: var(--spacing-2);
    }

    .prose .star-rating{
      justify-content: center;
      margin-bottom: var(--spacing-4);
    }

    .prose .h1{
      text-align: center;
      margin-top: 0;
      margin-bottom: var(--spacing-4);
    }

    .star-rating__wrapper{
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .mobile-full-width {
      margin-top: var(--spacing-4);
      margin-bottom: var(--spacing-1);
    }

    /* Mobile hero image spacing */
    .mobile-hero-image {
      margin-top: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }
  }

  /* Subheading typography styles */
  .subheading-text {
    {% if section.settings.subheading_font_family == 'heading' %}
      font-family: var(--heading-font-family);
    {% else %}
      font-family: var(--body-font-family);
    {% endif %}

    {% if section.settings.subheading_font_weight == 'normal' %}
      font-weight: 400;
    {% elsif section.settings.subheading_font_weight == 'medium' %}
      font-weight: 500;
    {% else %}
      font-weight: 700;
    {% endif %}

    margin-bottom: var(--spacing-4);
  }

  /* Responsive font sizing and alignment for subheading */
  @media screen and (max-width: 740px) {
    .subheading-text {
      font-size: {{ section.settings.subheading_font_size_mobile }}px;
      text-align: center;
      line-height: 1.4;
      margin-top: 0;
      margin-bottom: var(--spacing-6);
      max-width: {{ section.settings.subheading_mobile_max_width }}%;
      margin-left: auto;
      margin-right: auto;
    }
  }

  @media screen and (min-width: 741px) {
    .subheading-text {
      font-size: {{ section.settings.subheading_font_size_desktop }}px;
    }
  }
</style>

<div {% render 'section-properties', narrow: true %}>
  {%- comment -%}
  ----------------------------------------------------------------------------------------------------------------------
  MOBILE VARIATION
  ----------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}
  <images-with-text-scrolling class="images-scrolling-mobile scroll-area bleed sm:unbleed">
    {%- for block in section.blocks -%}
      <div class="images-scrolling-mobile__item snap-start" {{ block.shopify_attributes }}>
        <div class="images-scrolling__content">
          {%- if section.settings.show_counter -%}
            <span class="images-scrolling__counter bold">{{ forloop.index | prepend: '00' | slice: -2, 2 }}</span>
          {%- endif -%}

          <div class="prose">
            {%- if block.settings.title != blank -%}
              <p class="h1" {% if settings.heading_apparition != 'none' %}reveal-on-scroll="true"{% endif %}>
                {%- render 'styled-text', content: block.settings.title, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient, apparition_effect: true -%}
              </p>
            {%- endif -%}

            <!-- Always render the subheading element even if empty -->
            <p class="subheading-text">{{ block.settings.subheading | default: '' }}</p>

            <!-- Maximum resolution mobile hero image for 4x+ pixel density -->
            {%- if block.settings.mobile_image != blank -%}
              {%- if forloop.first -%}
                <img src="{{ block.settings.mobile_image | image_url: width: 1200 }}"
                     alt="{{ block.settings.mobile_image.alt | escape }}"
                     loading="eager"
                     fetchpriority="high"
                     decoding="async"
                     width="1200"
                     height="900"
                     class="rounded-sm mobile-hero-image">
              {%- else -%}
                {{- block.settings.mobile_image | image_url: width: 1200 | image_tag: loading: 'lazy', class: 'rounded-sm mobile-hero-image' -}}
              {%- endif -%}
            {%- elsif block.settings.image != blank -%}
              {%- if forloop.first -%}
                <img src="{{ block.settings.image | image_url: width: 1200 }}"
                     alt="{{ block.settings.image.alt | escape }}"
                     loading="eager"
                     fetchpriority="high"
                     decoding="async"
                     width="1200"
                     height="900"
                     class="rounded-sm mobile-hero-image">
              {%- else -%}
                {{- block.settings.image | image_url: width: 1200 | image_tag: loading: 'lazy', class: 'rounded-sm mobile-hero-image' -}}
              {%- endif -%}
            {%- else -%}
              {%- capture image_placeholder -%}product-{%- cycle '1', '2', '3', '4' -%}{%- endcapture -%}
              {{- image_placeholder | placeholder_svg_tag: 'placeholder mobile-hero-image' -}}
            {%- endif -%}

            <!-- Stars and rating after image -->
            {%- if block.settings.star_count > 0 -%}
              <div class="star-rating">
                <div class="star-rating__wrapper">
                  <div class="star-rating__stars-wrapper">
                    <div class="star-rating__stars">
                      {%- for i in (1..block.settings.star_count) -%}
                        {%- render 'icon' with 'rating-star', width: 24, height: 24 -%}
                      {%- endfor -%}
                    </div>
                  </div>
                  {%- if section.settings.rating_text != blank -%}
                    <div class="star-rating__text-wrapper">
                      <span class="star-rating__text">{{ section.settings.rating_text }}</span>
                    </div>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}

            <!-- Button after stars and rating -->
            {%- if block.settings.button_text != blank and block.settings.button_url != blank -%}
              <div class="mobile-full-width">
                {%- render 'button', content: block.settings.button_text, href: block.settings.button_url, style: block.settings.button_style, size: 'xl', class: 'mt-4', stretch: true, background: block.settings.button_color, text_color: block.settings.button_text_color -%}
              </div>
            {%- endif -%}

            {{- block.settings.content -}}
          </div>
        </div>
      </div>
    {%- endfor -%}
  </images-with-text-scrolling>

  {%- comment -%}
  ----------------------------------------------------------------------------------------------------------------------
  DESKTOP VARIATION
  ----------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}
  <images-with-text-scrolling scrolling-experience="{{ section.settings.desktop_image_effect }}" class="images-scrolling-desktop">
    <div class="images-scrolling-desktop__content-list">
      {%- for block in section.blocks -%}
        <div class="images-scrolling__content {% unless forloop.first %}opacity-0{% endunless %}">
          {%- if section.settings.show_counter -%}
            <span class="images-scrolling__counter bold">{{ forloop.index | prepend: '00' | slice: -2, 2 }}</span>
          {%- endif -%}

          <div class="prose">
            {%- if block.settings.star_count > 0 -%}
              <div class="star-rating">
                <div class="star-rating__wrapper">
                  <div class="star-rating__stars-wrapper">
                    <div class="star-rating__stars">
                      {%- for i in (1..block.settings.star_count) -%}
                        {%- render 'icon' with 'rating-star', width: 24, height: 24 -%}
                      {%- endfor -%}
                    </div>
                  </div>
                  {%- if section.settings.rating_text != blank -%}
                    <div class="star-rating__text-wrapper">
                      <span class="star-rating__text">{{ section.settings.rating_text }}</span>
                    </div>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}

            {%- if block.settings.title != blank -%}
              <p class="h1" {% if settings.heading_apparition != 'none' %}reveal-on-scroll="true"{% endif %}>
                {%- render 'styled-text', content: block.settings.title, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient, apparition_effect: true -%}
              </p>
            {%- endif -%}

            <!-- Always render the subheading element even if empty -->
            <p class="subheading-text">{{ block.settings.subheading | default: '' }}</p>

            {%- if block.settings.button_text != blank and block.settings.button_url != blank -%}
              {%- render 'button', content: block.settings.button_text, href: block.settings.button_url, style: block.settings.button_style, size: 'xl', class: 'mt-6', background: block.settings.button_color, text_color: block.settings.button_text_color -%}
            {%- endif -%}

            {{- block.settings.content -}}
          </div>
        </div>
      {%- endfor -%}
    </div>

    <div class="images-scrolling-desktop__media-wrapper">
      {%- for block in section.blocks -%}
        {%- capture loading_strategy -%}{% if forloop.first %}eager{% else %}lazy{% endif %}{%- endcapture -%}
        {%- capture fetch_priority -%}{% if forloop.first %}high{% else %}auto{% endif %}{%- endcapture -%}
        {%- capture decoding_strategy -%}{% if forloop.first %}sync{% else %}async{% endif %}{%- endcapture -%}
        {%- if block.settings.image != blank -%}
          {{- block.settings.image | image_url: width: 1200 | image_tag: loading: loading_strategy, fetchpriority: fetch_priority, decoding: decoding_strategy, sizes: '(max-width: 740px) 100vw, 65vw', widths: '400,600,800,1000,1200', class: 'rounded-sm' -}}
        {%- else -%}
          {%- capture image_placeholder -%}product-{%- cycle '1', '2', '3', '4' -%}{%- endcapture -%}
          {{- image_placeholder | placeholder_svg_tag: 'placeholder' -}}
        {%- endif -%}
      {%- endfor -%}
    </div>
  </images-with-text-scrolling>
</div>

{% schema %}
{
  "name": "Images and text scrolling",
  "tag": "section",
  "class": "shopify-section--images-and-text-scrolling",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "footer"]
  },
  "max_blocks": 15,
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_on_mobile",
      "label": "Stack on mobile",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_counter",
      "label": "Show counter",
      "default": true
    },
    {
      "type": "select",
      "id": "desktop_image_effect",
      "label": "Desktop image transition",
      "options": [
        {
          "value": "reveal",
          "label": "Reveal"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "reveal"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Image position",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "end"
    },
    {
      "type": "header",
      "content": "Floating Star"
    },
    {
      "type": "checkbox",
      "id": "show_floating_star",
      "label": "Show floating star",
      "default": true
    },
    {
      "type": "range",
      "id": "star_size",
      "min": 40,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Star size",
      "default": 90
    },
    {
      "type": "header",
      "content": "Mobile Star Position"
    },
    {
      "type": "range",
      "id": "star_mobile_top_position",
      "min": -300,
      "max": 300,
      "step": 30,
      "unit": "px",
      "label": "Star top position (Mobile)",
      "default": -60
    },
    {
      "type": "range",
      "id": "star_mobile_right_position",
      "min": -300,
      "max": 300,
      "step": 30,
      "unit": "px",
      "label": "Star right position (Mobile)",
      "default": 60
    },
    {
      "type": "header",
      "content": "Desktop Star Position"
    },
    {
      "type": "range",
      "id": "star_desktop_top_position",
      "min": -300,
      "max": 300,
      "step": 30,
      "unit": "px",
      "label": "Star top position (Desktop)",
      "default": 300
    },
    {
      "type": "range",
      "id": "star_desktop_right_position",
      "min": -300,
      "max": 300,
      "step": 30,
      "unit": "px",
      "label": "Star right position (Desktop)",
      "default": 0
    },
    {
      "type": "range",
      "id": "star_opacity",
      "min": 0.1,
      "max": 1.0,
      "step": 0.1,
      "label": "Star opacity",
      "default": 0.1
    },
    {
      "type": "image_picker",
      "id": "star_icon",
      "label": "Custom star icon",
      "info": "Optional. Default is icon-logo-star.svg"
    },
    {
      "type": "header",
      "content": "Subheading Typography"
    },
    {
      "type": "select",
      "id": "subheading_font_family",
      "label": "Font family",
      "options": [
        {
          "value": "heading",
          "label": "Heading font"
        },
        {
          "value": "body",
          "label": "Body font"
        }
      ],
      "default": "body"
    },
    {
      "type": "range",
      "id": "subheading_font_size_mobile",
      "min": 12,
      "max": 36,
      "step": 1,
      "unit": "px",
      "label": "Font size (Mobile)",
      "default": 20
    },
    {
      "type": "range",
      "id": "subheading_font_size_desktop",
      "min": 12,
      "max": 48,
      "step": 1,
      "unit": "px",
      "label": "Font size (Desktop)",
      "default": 20
    },
    {
      "type": "select",
      "id": "subheading_font_weight",
      "label": "Font weight",
      "options": [
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "bold",
          "label": "Bold"
        }
      ],
      "default": "normal"
    },
    {
      "type": "range",
      "id": "subheading_mobile_max_width",
      "min": 50,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Subheading max width (Mobile)",
      "default": 75,
      "info": "Controls line wrapping for mobile subheading text. Lower values create more line breaks."
    },
    {
      "type": "header",
      "content": "Rating"
    },
    {
      "type": "text",
      "id": "rating_text",
      "label": "Rating text",
      "default": "1000+ QUENCHED"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Desktop image",
          "info": "1200 x 1600px .jpg recommended"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image (optional)",
          "info": "If not provided, desktop image will be used. 600 x 800px .jpg recommended for mobile."
        },
        {
          "type": "range",
          "id": "star_count",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Number of stars",
          "default": 5
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "HYDRATION THAT POWERS PERFORMANCE"
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "URL"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "options": [
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "secondary",
              "label": "Secondary"
            }
          ],
          "default": "primary"
        },
        {
          "type": "color",
          "id": "button_color",
          "label": "Button color",
          "default": "#47DE47"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text color",
          "default": "#2A2A2A"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Images and text scrolling",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "title": "Heading 1",
            "star_count": 5
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Heading 2"
          }
        }
      ]
    }
  ]
}
{% endschema %}
