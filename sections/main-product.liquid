{%- render 'section-spacing-collapsing' -%}

{%- assign current_product = section.settings.product -%}
{%- if template == 'product' and product != blank -%}
  {%- assign current_product = product -%}
{%- endif -%}

<style>
  @media screen and (min-width: 999px) {
    .product-info:not(safe-sticky) {
      border: 3px solid !important;
      border-radius: 1rem;
      padding: 30px;
      /* background-color: #EDEDED; */
   }
  }

  buy-buttons.sm\:hidden {
      width: 100%;
  }

  /* Include custom sticky cart CSS */
  {% render 'sticky-cart-custom' %}

  #shopify-section-{{ section.id }} {
    --product-grid: auto / minmax(0, 1fr);
    --product-gallery-media-list-grid: auto / auto-flow {% if section.settings.mobile_carousel_control == 'free_scroll' %}{% if section.settings.mobile_media_size == 'expanded' %}84vw{% else %}73vw{% endif %}{% else %}100%{% endif %};
    --product-gallery-media-list-gap: {% if section.settings.mobile_media_size == 'expanded' %}var(--spacing-0-5){% else %}var(--grid-gutter){% endif %};
  }

  .section.section--tight.section-blends.section-full {
    padding-block-start: 30px !important;
  }

  @media screen and (max-width: 999px) {
    .section.section--tight.section-blends.section-full {
      padding-block-start: {{section.settings.section_spacing_block_start_mobile}}px !important;
    }
    #shopify-section-{{ section.id }} {
      margin-top: {% if section.settings.mobile_media_size == 'expanded' %}
        0px
      {% else %}
        {{ section.settings.section_spacing_block_start_mobile | default: section.settings.section_spacing_block_start }}px{% endif %};
    }
  }

  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} {
    {%- assign media_ratio = section.settings.desktop_media_width | divided_by: 50.0 -%}
      --product-grid: auto / minmax(0, {{ media_ratio }}fr) minmax(0, {{ 2.0 | minus: media_ratio }}fr);
      --product-gallery-media-list-grid: {% if section.settings.desktop_media_layout contains 'grid' %}auto-flow dense / repeat(2, minmax(0, 1fr)){% else %}auto / auto-flow 100%{% endif %};
      --product-gallery-media-list-gap: calc(var(--grid-gutter) / 2);
    }

  {%- if section.settings.desktop_media_layout == 'grid_highlight' -%}
    #shopify-section-{{ section.id }} .product-gallery__media-list > :not([hidden]) {
      grid-column: span 2;
    }

    #shopify-section-{{ section.id }} .product-gallery__media-list > :not([hidden]) ~ *:not(.product-gallery__media--expand) {
      grid-column: span 1;
    }
  {%- endif -%}
  }

  @media screen and (min-width: 1400px) {
    #shopify-section-{{ section.id }} {
      --product-gallery-media-list-gap: var(--grid-gutter);
    }
  }

  {% if block.settings.button_font_family == 'heading' %}
  .product-info__buy-buttons .button {
    font-family: var(--heading-font-family);
    font-weight: {{ block.settings.button_font_weight }};
  }
  {% endif %}

  .product-info__price {
    {% if block.settings.price_layout == 'inline' %}
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    {% endif %}
  }

  .product-info__price .price-list {
    {% if block.settings.price_font_family %}
    font-family: '{{ block.settings.price_font_family }}';
    {% endif %}
  }

  .product-info__price .secondary-text {
    {% if block.settings.price_layout == 'inline' %}
    {% if block.settings.secondary_text_alignment == 'right' %}
    margin-left: auto;
    {% endif %}
    {% endif %}
  }

  /* Ensure the sticky bar doesn't overlap with footer */
  .shopify-section--footer {
    position: relative;
    z-index: 51;
  }


  @media screen and (max-width: 999px) {
    #shopify-section-{{ section.id }} .product-quick-add {
      padding: var(--spacing-3);
    }
  }

  button.button.button--lg.button--secondary.w-full {
      font-family: var(--heading-font-family);
      font-weight: 700;
      font-size: 1.2em;
  }
</style>

{%- assign update_url = false -%}
{%- if template == 'product' -%}
  {%- assign update_url = true -%}
{%- endif -%}

<div {% render 'section-properties', tight: true %}>
  {%- capture product_form_id -%}product-form-{{ current_product.id }}-{{ section.id }}{%- endcapture -%}

    <product-rerender id="product-info-{{ current_product.id }}-{{ section.id }}" observe-form="{{ product_form_id }}" allow-partial-rerender>
    <div class="product">
      {%- if current_product != blank -%}
        <div class="product-media-section">
          {%- if current_product.media.size > 0 -%}
            {%- render 'product-gallery', product: current_product, product_form_id: product_form_id -%}

            {%- for block in section.blocks -%}
              {%- if block.type == 'badges' -%}
                <div class="product-badges product-badges--desktop {% if block.settings.badges_layout == 'grid' %}grid-layout{% else %}flex-layout{% endif %}" {{ block.shopify_attributes }}>
                  {%- for i in (1..8) -%}
                    {%- assign icon_setting = 'badge_' | append: i | append: '_icon' -%}
                    {%- assign text_setting = 'badge_' | append: i | append: '_text' -%}
                    {%- if block.settings[icon_setting] != blank or block.settings[text_setting] != blank -%}
                      <div class="product-badge">
                        <span class="badge-icon">0</span>
                        {%- if block.settings[text_setting] != blank -%}
                          <span class="badge-text">{{ block.settings[text_setting] }}</span>
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>

                <style>
                  .product-media-section {
                    display: flex;
                    flex-direction: column;
                    gap: var(--spacing-4);
                  }

                  .product-badges {
                    margin-top: var(--spacing-4);
                    gap: var(--spacing-4);
                  }

                  .product-badges.grid-layout {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                  }

                  .product-badges.flex-layout {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: center;
                  }

                  .product-badge {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    gap: var(--spacing-2);
                  }


                  .badge-icon {
                    font-size: 40px;
                    font-weight: 400;
                    line-height: 50px;
                    font-family: 'Meltmino';
                  }

                  .badge-text {
                    font-size: var(--{{ block.settings.badge_font_size }});
                    /* color: rgb(var(--text-color)); */
                    font-family: var(--{{ block.settings.badge_font_family }}-font-family);
                  }

                  @media screen and (min-width: 1000px) {
                    .product-badges--mobile {
                      display: none !important;
                    }
                  }

                  @media screen and (max-width: 999px) {
                    .product-badges--desktop {
                      display: none !important;
                    }

                    .product-badges.grid-layout {
                      grid-template-columns: repeat(2, 1fr);
                    }

                    .product-badge {
                      gap: var(--spacing-1);
                    }

                    .badge-icon {
                      width: 40px;
                      height: 40px;
                    }

                    .product-badges--mobile {
                      margin-block: 0;
                      margin-top: 0 !important;
                      margin-bottom: var(--spacing-16) !important;
                      padding: var(--spacing-10) 0px;
                      padding-inline: var(--container-gutter);
                      background-color: #2A2A2A !important;
                      color: white !important;
                    }
                  }

                  .benefits-list li {
                    /* background: #2A2A2A; */
                    /* color: white; */
                    border-radius: var(--rounded-button);
                    display: flex;
                    /* justify-content: center;
                    align-items: center; */
                    /* height: 55px; */
                  }

                  .accordion__toggle.bold {
                    font-weight: normal;
                  }

                  .benefits-list li img {
                    /* display: none; */
                  }
                  .benefits-list li span {
                    text-align: center;
                  }
                  .offer-grid__item {
                    border: 2px solid;
                    border-color: #2A2A2A;
                    border-radius: 0.5rem;
                  }

                </style>
              {%- endif -%}
            {%- endfor -%}
          </div>
        {%- endif -%}

<div class="product-info">
{%- render 'product-info',
product: current_product,
product_form_id: product_form_id,
update_url: update_url,
context: template == 'product' | append: '_product' | prepend: 'main_'
-%}
</div>
      {%- else -%}
        <product-gallery form="placeholder-{{ section.id }}" class="product-gallery {% if section.settings.mobile_carousel_control contains 'dots' %}product-gallery--mobile-dots{% endif %} {% if section.settings.desktop_media_layout contains 'grid' %}product-gallery--desktop-grid{% else %}product-gallery--desktop-carousel{% endif %} {% if section.settings.desktop_media_layout == 'carousel_thumbnails_left' %}product-gallery--desktop-thumbnails-left{% endif %} {% if section.settings.mobile_media_size == 'expanded' %}product-gallery--mobile-expanded{% endif %}">
          <div class="product-gallery__ar-wrapper">
            <div class="product-gallery__media-list-wrapper">
              <media-carousel desktop-mode="{{ section.settings.desktop_media_layout }}" adaptive-height class="product-gallery__media-list {% if section.settings.mobile_media_size == 'expanded' %}full-bleed{% else %}bleed{% endif %} scroll-area md:unbleed">
                <div class="product-gallery__media {% if section.settings.mobile_carousel_control != 'free_scroll' %}snap-center{% endif %}">
                  {{- 'product-1' | placeholder_svg_tag: 'placeholder rounded' -}}
                </div>
              </media-carousel>
            </div>
          </div>
        </product-gallery>

        <safe-sticky class="product-info">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'vendor' -%}
                <div class="product-info__vendor" {{ block.shopify_attributes }}>{{- 'general.on_boarding.product_vendor' | t -}}</div>

              {%- when 'title' -%}
                <h2 class="product-info__title {{ block.settings.heading_tag }}" {{ block.shopify_attributes }}>{{ 'general.on_boarding.product_title' | t }}</h2>

              {%- when 'price' -%}
                <div class="product-info__price">
                  <price-list class="price-list {{ block.settings.price_size }}">
                    <sale-price class="{{ block.settings.price_size }}">{{ 4000 | money }}</sale-price>
                  </price-list>
                  {%- if block.settings.secondary_text != blank -%}
                    <div class="secondary-text {{ block.settings.secondary_text_size }}">{{ block.settings.secondary_text }}</div>
                  {%- endif -%}
                </div>

              {%- when 'separator' -%}
                <hr class="product-info__separator" {{ block.shopify_attributes }}>

              {%- when 'description' -%}
                <div class="product-info__description" {{ block.shopify_attributes }}>
                  <div class="prose">
                    {{- 'general.on_boarding.product_description' | t -}}
                  </div>
                </div>

              {%- when 'benefits' -%}
                {%- render 'benefits-list', block: block -%}

              {%- when 'quantity_selector' -%}
                <div class="product-info__quantity-selector {{ block.shopify_attributes }}">
                  {%- assign variant = product.selected_or_first_available_variant -%}

                  <div class="form-control">
                    <label for="{{ product_form_id }}-quantity" class="block-label text-subdued">{{- 'product.quantity.label' | t -}}:</label>

                    <quantity-selector class="quantity-selector">
                      <button type="button" class="quantity-selector__button" aria-label="{{ 'product.quantity.decrease_quantity' | t }}">{% render 'icon' with 'minus', width: 10, height: 2 %}</button>
                      <input id="{{ product_form_id }}-quantity" type="number" is="quantity-input" inputmode="numeric" class="quantity-selector__input" name="quantity" form="{{ product_form_id }}" value="{{ variant.quantity_rule.min | default: 1 }}" step="{{ variant.quantity_rule.increment }}" min="{{ variant.quantity_rule.min }}" {% if variant.quantity_rule.max != nil %}max="{{ variant.quantity_rule.max }}"{% endif %} autocomplete="off">
                      <button type="button" class="quantity-selector__button" aria-label="{{ 'product.quantity.increase_quantity' | t }}">{% render 'icon' with 'plus', width: 10, height: 10 %}</button>
                    </quantity-selector>
                  </div>

                  {%- liquid
                    assign quantity_rules = ''

                    if variant.quantity_rule.min > 1
                      assign rule = 'product.quantity.minimum_of' | t: min: variant.quantity_rule.min
                      assign quantity_rules = quantity_rules | append: ' / ' | append: rule
                    endif

                    if variant.quantity_rule.max != nil
                      assign rule = 'product.quantity.maximum_of' | t: max: variant.quantity_rule.max
                      assign quantity_rules = quantity_rules | append: ' / ' | append: rule
                    endif

                    if variant.quantity_rule.increment > 1
                      assign rule = 'product.quantity.increment_of' | t: step: variant.quantity_rule.increment
                      assign quantity_rules = quantity_rules | append: ' / ' | append: rule
                    endif
                  -%}

                  {%- if quantity_rules != blank -%}
                    <p class="text-subdued text-sm">{{ quantity_rules | remove_first: ' / ' | capitalize }}</p>
                  {%- endif -%}
                </div>

              {%- when 'offer_grid' -%}
                <div class="offer-grid {% if block.settings.desktop_layout == 'columns' %}offer-grid--columns{% endif %} {% if block.settings.mobile_layout == 'columns' %}offer-grid--mobile-columns{% endif %}">
                  {%- for i in (1..3) -%}
                    {%- assign title_key = 'title_' | append: i -%}
                    {%- assign content_key = 'content_' | append: i -%}

                    {%- if block.settings[title_key] != blank or block.settings[content_key] != blank -%}
                      <div class="offer-grid__item">
                        <div class="offer {% if block.settings.text_alignment == 'center' %}offer--center{% endif %} {{ block.settings.border_radius }}"
                             style="background-color: {{ block.settings.background_color }};">
                          <div class="{{ block.settings.title_font_size }}"
                               style="font-family: var(--{{ block.settings.title_font_family }}-font-family);
                                      font-weight: {{ block.settings.title_font_weight }};
                                      color: {{ block.settings.title_color }};">
                            {{ block.settings[title_key] }}
                          </div>
                          {%- if block.settings[content_key] != blank -%}
                            <div class="prose {{ block.settings.content_font_size }}"
                                 style="font-family: var(--{{ block.settings.content_font_family }}-font-family);
                                        font-weight: {{ block.settings.content_font_weight }};
                                        color: {{ block.settings.content_color }};">
                              {{ block.settings[content_key] }}
                            </div>
                          {%- endif -%}
                        </div>
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>

                <style>
                  .offer-grid {
                    display: grid;
                    gap: var(--spacing-4);
                  }

                  .offer-grid .offer {
                    padding: var(--spacing-4);
                  }

                  @media screen and (min-width: 1000px) {
                    .offer-grid--columns {
                      grid-template-columns: repeat(3, 1fr);
                    }
                  }

                  @media screen and (max-width: 999px) {
                    .offer-grid--mobile-columns {
                      grid-template-columns: repeat(2, 1fr);
                    }
                  }
                </style>

              {%- when 'buy_buttons' -%}
                <div class="product-info__buy-buttons" {{ block.shopify_attributes }}>
                  <buy-buttons class="buy-buttons">
                    {%- capture button_content -%}{{ 'product.general.add_to_cart_button' | t }}{%- endcapture -%}
                    {%- render 'button',
                      content: button_content,
                      type: 'submit',
                      size: 'xl',
                      background: block.settings.atc_button_background,
                      text_color: block.settings.atc_button_text_color,
                      font_family: 'heading',
                      font_weight: 700
                    -%}
                  </buy-buttons>
                </div>

                {%- for block_inner in section.blocks -%}
                  {%- if block_inner.type == 'badges' -%}
                    <div class="product-badges product-badges--mobile {% if block_inner.settings.badges_layout == 'grid' %}grid-layout{% else %}flex-layout{% endif %}" {{ block_inner.shopify_attributes }}>
                      {%- for i in (1..8) -%}
                        {%- assign icon_setting = 'badge_' | append: i | append: '_icon' -%}
                        {%- assign text_setting = 'badge_' | append: i | append: '_text' -%}
                        {%- if block_inner.settings[icon_setting] != blank or block_inner.settings[text_setting] != blank -%}
                          <div class="product-badge">
                            <span class="badge-icon">0</span>
                            {%- if block_inner.settings[text_setting] != blank -%}
                              <span class="badge-text">{{ block_inner.settings[text_setting] }}</span>
                            {%- endif -%}
                          </div>
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  {%- endif -%}
                {%- endfor -%}
            {%- endcase -%}
          {%- endfor -%}
        </safe-sticky>
      {%- endif -%}
    </div>
  </product-rerender>
</div>

{%- for block in section.blocks -%}
{%- if block.type == 'badges' -%}
<div class="product-badges product-badges--mobile {% if block.settings.badges_layout == 'grid' %}grid-layout{% else %}flex-layout{% endif %}" {{ block.shopify_attributes }}>
{%- for i in (1..8) -%}
{%- assign icon_setting = 'badge_' | append: i | append: '_icon' -%}
{%- assign text_setting = 'badge_' | append: i | append: '_text' -%}
{%- if block.settings[icon_setting] != blank or block.settings[text_setting] != blank -%}
<div class="product-badge">
<span class="badge-icon">0</span>
{%- if block.settings[text_setting] != blank -%}
<span class="badge-text">{{ block.settings[text_setting] }}</span>
{%- endif -%}
</div>
{%- endif -%}
{%- endfor -%}
</div>
{%- endif -%}
{%- endfor -%}

{%- comment -%}
IMPLEMENTATION NOTE: Shopify does not currently allows to render a given section within the context of a given product. However,
when rendering the quick buy popovers, we want to be able to re-use the merchant's choices (such as the selector type). This
is however only possible by rendering the whole product page, and extracting the relevant part. Here, we therefore render the
quick buy information in a template, that will be extracted in JS, but ensure it is not visible in the main product page
{%- endcomment -%}

{%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

{%- render 'custom-sticky-add-to-cart', product: product, product_form_id: product_form_id, buy_buttons_block: buy_buttons_block -%}

{%- render 'product-quick-buy', product: product -%}

{% schema %}
{
  "name": "Product page",
  "class": "shopify-section--main-product",
  "tag": "section",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Section spacing"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "product",
      "id": "product",
      "label": "Product"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "range",
      "id": "desktop_media_width",
      "label": "Desktop media size",
      "min": 35,
      "max": 65,
      "step": 5,
      "unit": "%",
      "default": 55
    },
    {
      "type": "select",
      "id": "desktop_media_layout",
      "label": "Desktop media layout",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "grid_highlight",
          "label": "Grid with main media"
        },
        {
          "value": "carousel_thumbnails_left",
          "label": "Thumbnails left (carousel)"
        },
        {
          "value": "carousel_thumbnails_bottom",
          "label": "Thumbnails bottom (carousel)"
        }
      ],
      "default": "carousel_thumbnails_left"
    },
    {
      "type":"select",
      "id": "mobile_media_size",
      "label": "Mobile media size",
      "options": [
        {
          "value": "expanded",
          "label": "Expanded"
        },
        {
          "value": "contained",
          "label": "Contained"
        }
      ],
      "default": "expanded"
    },
    {
      "type": "select",
      "id": "mobile_carousel_control",
      "label": "Mobile carousel control",
      "options": [
        {
          "value": "dots",
          "label": "Dots"
        },
        {
          "value": "floating_dots",
          "label": "Floating dots"
        },
        {
          "value": "thumbnails",
          "label": "Thumbnails"
        },
        {
          "value": "free_scroll",
          "label": "Free scroll"
        }
      ],
      "default": "floating_dots"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay. Grid mode on desktop turn off autoplay.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": false
    },
    {
      "type": "header",
      "content": "Image zoom"
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable",
      "default": true
    },
    {
      "type": "range",
      "id": "max_image_zoom_level",
      "min": 1,
      "max": 4,
      "step": 0.5,
      "label": "Max zoom level",
      "default": 3
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Input colors",
      "info": "Applies to elements like quantity selector and variant selectors."
    },
    {
      "type": "color",
      "id": "input_background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "input_text_color",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "vendor",
      "name": "Vendor",
      "limit": 1
    },
    {
      "type": "title",
      "name": "Title",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Style",
          "options": [
            {
              "value": "h1",
              "label": "X-Large"
            },
            {
              "value": "h2",
              "label": "Large"
            },
            {
              "value": "h3",
              "label": "Medium"
            },
            {
              "value": "h4",
              "label": "Small"
            },
            {
              "value": "h5",
              "label": "X-Small"
            },
            {
              "value": "h6",
              "label": "XX-Small"
            }
          ],
          "default": "h2"
        }
      ]
    },
    {
      "type": "sku",
      "name": "SKU",
      "limit": 1
    },
    {
      "type": "badges",
      "name": "Product Badges",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Badge 1"
        },
        {
          "type": "image_picker",
          "id": "badge_1_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_1_text",
          "label": "Text",
          "default": "Zero Sugar"
        },
        {
          "type": "header",
          "content": "Badge 2"
        },
        {
          "type": "image_picker",
          "id": "badge_2_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_2_text",
          "label": "Text",
          "default": "Gluten Free"
        },
        {
          "type": "header",
          "content": "Badge 3"
        },
        {
          "type": "image_picker",
          "id": "badge_3_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_3_text",
          "label": "Text",
          "default": "Vegan"
        },
        {
          "type": "header",
          "content": "Badge 4"
        },
        {
          "type": "image_picker",
          "id": "badge_4_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_4_text",
          "label": "Text",
          "default": "Soy Free"
        },
        {
          "type": "header",
          "content": "Badge 5"
        },
        {
          "type": "image_picker",
          "id": "badge_5_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_5_text",
          "label": "Text",
          "default": "Non GMO"
        },
        {
          "type": "header",
          "content": "Badge 6"
        },
        {
          "type": "image_picker",
          "id": "badge_6_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_6_text",
          "label": "Text",
          "default": "Caffeine Free"
        },
        {
          "type": "header",
          "content": "Badge 7"
        },
        {
          "type": "image_picker",
          "id": "badge_7_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_7_text",
          "label": "Text",
          "default": "Keto Friendly"
        },
        {
          "type": "header",
          "content": "Badge 8"
        },
        {
          "type": "image_picker",
          "id": "badge_8_icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "badge_8_text",
          "label": "Text",
          "default": "No Additives"
        },
        {
          "type": "select",
          "id": "badge_font_family",
          "label": "Badge font family",
          "options": [
            {
              "value": "heading",
              "label": "Heading Font"
            },
            {
              "value": "body",
              "label": "Body Font"
            }
          ],
          "default": "body"
        },
        {
          "type": "select",
          "id": "badge_font_size",
          "label": "Badge font size",
          "options": [
            {
              "value": "text-xxs",
              "label": "XX-Small"
            },
            {
              "value": "text-xs",
              "label": "X-Small"
            },
            {
              "value": "text-s",
              "label": "Small"
            },
            {
              "value": "font-size-base",
              "label": "Medium"
            }
          ],
          "default": "text-xxs"
        },
        {
          "type": "select",
          "id": "badges_layout",
          "label": "Layout",
          "options": [
            {
              "value": "grid",
              "label": "Grid"
            },
            {
              "value": "flex",
              "label": "Flex"
            }
          ],
          "default": "grid"
        }
      ]
    },
    {
      "type": "price",
      "name": "Price",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "price_font_family",
          "label": "Price font family",
          "options": [
            {
              "value": "FormaDJRBanner",
              "label": "FormaDJR Banner"
            },
            {
              "value": "FormaDJRDisplay",
              "label": "FormaDJR Display"
            },
            {
              "value": "FormaDJRText",
              "label": "FormaDJR Text"
            },
            {
              "value": "FormaDJRDeck",
              "label": "FormaDJR Deck"
            },
            {
              "value": "FormaDJRMicro",
              "label": "FormaDJR Micro"
            }
          ],
          "default": "FormaDJRText"
        },
        {
          "type": "select",
          "id": "price_size",
          "label": "Price size",
          "options": [
            {
              "value": "text-sm",
              "label": "Small"
            },
            {
              "value": "text-base",
              "label": "Medium"
            },
            {
              "value": "text-lg",
              "label": "Large"
            },
            {
              "value": "text-xl",
              "label": "X-Large"
            },
            {
              "value": "text-2xl",
              "label": "2X-Large"
            },
            {
              "value": "text-3xl",
              "label": "3X-Large"
            }
          ],
          "default": "text-xl"
        },
        {
          "type": "select",
          "id": "price_layout",
          "label": "Layout",
          "options": [
            {
              "value": "stacked",
              "label": "Stacked"
            },
            {
              "value": "inline",
              "label": "Inline"
            }
          ],
          "default": "inline"
        },
        {
          "type": "checkbox",
          "id": "show_taxes_notice",
          "label": "Show taxes notice",
          "default": false
        },
        {
          "type": "text",
          "id": "secondary_text",
          "label": "Secondary Text",
          "info": "Example: '40 Servings (£1.20 / serving)'"
        },
        {
          "type": "select",
          "id": "secondary_text_size",
          "label": "Secondary Text Size",
          "options": [
            {
              "value": "text-xs",
              "label": "Extra Small"
            },
            {
              "value": "text-sm",
              "label": "Small"
            },
            {
              "value": "text-base",
              "label": "Medium"
            }
          ],
          "default": "text-sm"
        },
        {
          "type": "select",
          "id": "secondary_text_alignment",
          "label": "Secondary Text Alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "info": "Only applies when layout is set to inline"
        }
      ]
    },
    {
      "type": "payment_terms",
      "name": "Payment installments",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To display payment installments, your store needs to support Shop Pay Installments. [Learn more](https://help.shopify.com/en/manual/payments/shop-pay-installments)"
        }
      ]
    },
    {
      "type": "rating",
      "name": "Rating",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To display a rating, add a product rating app. [Learn more](https://apps.shopify.com/categories/store-design-social-proof-product-reviews)"
        },
        {
          "type": "checkbox",
          "id": "show_empty",
          "label": "Show if no reviews",
          "default": false
        },
        {
          "type": "text",
          "id": "rating_text",
          "label": "Rating Text",
          "default": "Rated 4.9 | 27,956 Reviews",
          "info": "Text to display next to rating stars"
        },
        {
          "type": "select",
          "id": "rating_text_font_family",
          "label": "Font Family",
          "options": [
            {
              "value": "FormaDJRBanner",
              "label": "FormaDJR Banner"
            },
            {
              "value": "FormaDJRDisplay",
              "label": "FormaDJR Display"
            },
            {
              "value": "FormaDJRText",
              "label": "FormaDJR Text"
            },
            {
              "value": "FormaDJRDeck",
              "label": "FormaDJR Deck"
            },
            {
              "value": "FormaDJRMicro",
              "label": "FormaDJR Micro"
            }
          ],
          "default": "FormaDJRText"
        },
        {
          "type": "select",
          "id": "rating_text_size",
          "label": "Font Size",
          "options": [
            {
              "value": "text-xs",
              "label": "Extra Small"
            },
            {
              "value": "text-sm",
              "label": "Small"
            },
            {
              "value": "text-base",
              "label": "Medium"
            },
            {
              "value": "text-lg",
              "label": "Large"
            }
          ],
          "default": "text-sm"
        },
        {
          "type": "range",
          "id": "rating_text_weight",
          "min": 300,
          "max": 900,
          "step": 100,
          "label": "Font Weight",
          "default": 400
        }
      ]
    },
    {
      "type": "separator",
      "name": "Separator"
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "collapse_content",
          "label": "Collapse content",
          "default": false
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "hide_sold_out_variants",
          "label": "Hide sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "stack_blocks",
          "label": "Stack options on mobile",
          "default": true
        },
        {
          "type": "select",
          "id": "selector_style",
          "label": "Selector style",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "swatch_selector_style",
          "label": "Swatch selector style",
          "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.",
          "options": [
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "block_swatch",
              "label": "Block with swatch"
            },
            {
              "value": "none",
              "label": "None"
            }
          ],
          "default": "swatch"
        },
        {
          "type": "text",
          "id": "variant_image_options",
          "label": "Show variant image for options",
          "info": "List of comma separated option names where option values show the attached variant image. [Learn more](https://impact-theme.helpscoutdocs.com/article/555-variant-images-for-color-option)"
        },
        {
          "type": "page",
          "id": "size_chart_page",
          "label": "Size chart page",
          "info": "Feature a page for size option"
        }
      ]
    },
    {
      "type": "product_variations",
      "name": "Product variations",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Link products of different colors using swatches. [Learn more](https://support.maestrooo.com/article/307-product-creating-product-variations-that-link-to-different-pages)"
        },
        {
          "type": "text",
          "id": "option_name",
          "label": "Option name",
          "info": "Example: Color, Style..."
        },
        {
          "type": "text",
          "id": "option_value_metafield",
          "label": "Option value metafield",
          "info": "Enter namespace and key of the metafield holding the value. Eg.: custom.color"
        },
        {
          "type": "product_list",
          "id": "products",
          "label": "Products",
          "info": "Select all the variations (including the product itself)."
        },
        {
          "type": "select",
          "id": "selector_style",
          "label": "Selector style",
          "info": "Product image mode requires that all variant have an associated image. [Learn more](https://help.shopify.com/en/manual/products/product-media/add-images-variants#add-images-to-existing-variants)",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "block_swatch",
              "label": "Block with swatch"
            },
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "variant_image",
              "label": "Variant image"
            }
          ],
          "default": "swatch"
        }
      ]
    },
    {
      "type": "line_item_property",
      "name": "Line item property",
      "settings": [
        {
          "type": "paragraph",
          "content": "Line item properties are used to collect customization information for an item added to the cart."
        },
        {
          "type": "text",
          "id": "label",
          "label": "Label",
          "default": "Your label"
        },
        {
          "type": "select",
          "id": "type",
          "label": "Type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "checkbox",
              "label": "Checkbox"
            }
          ],
          "default": "text"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "info": "For text property, the customer must write a text ; for checkbox, the customer must check it to add to cart.",
          "default": false
        },
        {
          "type": "header",
          "content": "Text",
          "info": "Only applicable for line item property of type Text."
        },
        {
          "type": "checkbox",
          "id": "allow_long_text",
          "label": "Allow long text",
          "default": false
        },
        {
          "type": "number",
          "id": "max_length",
          "label": "Maximum number of characters"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "The selector is automatically hidden if all variants are sold out. When at least one variant is available, the selector is always visible to prevent the page from moving when switching variants. To show a volume pricing table, add the dedicatd Volume pricing block."
        }
      ]
    },
    {
      "type": "volume_pricing",
      "name": "Volume pricing table",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Volume pricing is only available to Shopify Plus merchants. [Learn more](https://help.shopify.com/en/manual/b2b/catalogs/quantity-pricing#volume-pricing)"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "low_inventory_threshold",
          "label": "Low inventory threshold",
          "info": "Use low stock color when quantity is below the threshold. Choose 0 to always show in stock.",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "label": "Show recipient information form for gift cards",
          "info": "Allow buyers to send gift cards along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)",
          "default": true
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color"
        },
        {
          "type": "color",
          "id": "payment_button_background",
          "label": "Buy now button background"
        },
        {
          "type": "color",
          "id": "payment_button_text_color",
          "label": "Buy now button color"
        }
      ]
    },
    {
      "type": "pickup_availability",
      "name": "Pickup availability",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Allow your customers to see availability in retail stores by [setting up local pickup](https://help.shopify.com/en/manual/sell-in-person/shopify-pos/order-management/local-pickup-for-online-orders)."
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Share some content to your customers about your products.</p>"
        }
      ]
    },
    {
      "type": "collapsible_text",
      "name": "Collapsible text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Share some content to your customers about your products.</p>"
        },
        {
          "type": "page",
          "id": "page",
          "label": "Page",
          "info": "Replaces inline content if specified."
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ],
          "default": "start"
        },
        {
          "type": "range",
          "id": "max_width",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Width",
          "default": 150
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create link to your contact page, external marketplace..."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Button"
        },
        {
          "type": "select",
          "id": "size",
          "label": "Size",
          "options": [
            {
              "value": "sm",
              "label": "Small"
            },
            {
              "value": "base",
              "label": "Medium"
            },
            {
              "value": "lg",
              "label": "Large"
            },
            {
              "value": "xl",
              "label": "X-Large"
            }
          ],
          "default": "xl"
        },
        {
          "type": "select",
          "id": "style",
          "label": "Style",
          "options": [
            {
              "value": "outline",
              "label": "Outline"
            },
            {
              "value": "fill",
              "label": "Fill"
            }
          ],
          "default": "fill"
        },
        {
          "type": "checkbox",
          "id": "stretch",
          "label": "Stretch",
          "default": true
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Custom Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "associated_products",
      "name": "Complementary products",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To select complementary products, use the Search & Discovery app. [Learn more](https://help.shopify.com/en/manual/online-store/search-and-discovery/product-recommendations#complementary-products)"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Buy it with"
        },
        {
          "type": "checkbox",
          "id": "stack_products",
          "label": "Stack products",
          "default": true
        },
        {
          "type": "range",
          "id": "products_count",
          "min": 1,
          "max": 10,
          "label": "Products to show",
          "default": 5
        },
        {
          "type": "header",
          "content": "Colors",
          "info": "Cards are bordered when background matches section background."
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "offer",
      "name": "Offer",
      "settings": [
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            }
          ],
          "default": "start"
        },
        {
          "type": "select",
          "id": "icon_position",
          "label": "Icon position",
          "options": [
            {
              "value": "aligned",
              "label": "Aligned horizontally"
            },
            {
              "value": "stacked",
              "label": "Stacked"
            }
          ],
          "default": "aligned"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "Icon",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-percent",
              "label": "Percent",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-star",
              "label": "Star",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-building",
              "label": "Building",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-award-gift",
              "label": "Award gift",
              "group": "Shop"
            },
            {
              "value": "picto-happy",
              "label": "Happy",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-pin",
              "label": "Pin",
              "group": "Shipping"
            },
            {
              "value": "picto-timer",
              "label": "Timer",
              "group": "Shipping"
            },
            {
              "value": "picto-validation",
              "label": "Validation",
              "group": "Shipping"
            },
            {
              "value": "picto-truck",
              "label": "Truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return",
              "label": "Return",
              "group": "Shipping"
            },
            {
              "value": "picto-earth",
              "label": "Earth",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-profile",
              "label": "Secure profile",
              "group": "Payment & Security"
            },
            {
              "value": "picto-money",
              "label": "Money",
              "group": "Payment & Security"
            },
            {
              "value": "picto-recycle",
              "label": "Recycle",
              "group": "Ecology"
            },
            {
              "value": "picto-leaf",
              "label": "Leaf",
              "group": "Ecology"
            },
            {
              "value": "picto-tree",
              "label": "Tree",
              "group": "Ecology"
            },
            {
              "value": "picto-mobile-phone",
              "label": "Mobile phone",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Operator",
              "group": "Communication"
            },
            {
              "value": "picto-mailbox",
              "label": "Mailbox",
              "group": "Communication"
            },
            {
              "value": "picto-envelope",
              "label": "Envelope",
              "group": "Communication"
            },
            {
              "value": "picto-comment",
              "label": "Comment",
              "group": "Communication"
            },
            {
              "value": "picto-question",
              "label": "Question",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-at-sign",
              "label": "At sign",
              "group": "Tech"
            },
            {
              "value": "picto-camera",
              "label": "Camera",
              "group": "Tech"
            },
            {
              "value": "picto-wifi",
              "label": "WiFi",
              "group": "Tech"
            },
            {
              "value": "picto-bluetooth",
              "label": "Bluetooth",
              "group": "Tech"
            },
            {
              "value": "picto-printer",
              "label": "Printer",
              "group": "Tech"
            },
            {
              "value": "picto-smart-watch",
              "label": "Smart watch",
              "group": "Tech"
            },
            {
              "value": "picto-coffee",
              "label": "Coffee",
              "group": "Food & Drink"
            },
            {
              "value": "picto-burger",
              "label": "Burger",
              "group": "Food & Drink"
            },
            {
              "value": "picto-beer",
              "label": "Beer",
              "group": "Food & Drink"
            },
            {
              "value": "picto-target",
              "label": "Target",
              "group": "Other"
            },
            {
              "value": "picto-document",
              "label": "Document",
              "group": "Other"
            },
            {
              "value": "picto-jewelry",
              "label": "Jewelry",
              "group": "Other"
            },
            {
              "value": "picto-music",
              "label": "Music",
              "group": "Other"
            },
            {
              "value": "picto-file",
              "label": "File",
              "group": "Other"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Other"
            },
            {
              "value": "picto-stop",
              "label": "Stop",
              "group": "Other"
            }
          ],
          "default": "picto-coupon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon",
          "info": "240 x 240px .png recommended"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 20,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Icon width",
          "default": 24
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Shipping"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Short content about your shipping rates or discounts.</p>"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "#eaf2ed"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "#00a341"
        }
      ]
    },
    {
      "type": "offer_grid",
      "name": "Offers Grid",
      "settings": [
        {
          "type": "select",
          "id": "desktop_layout",
          "label": "Desktop Layout",
          "options": [
            {
              "value": "row",
              "label": "Single Row"
            },
            {
              "value": "columns",
              "label": "Three Columns"
            }
          ],
          "default": "row"
        },
        {
          "type": "select",
          "id": "mobile_layout",
          "label": "Mobile Layout",
          "options": [
            {
              "value": "stacked",
              "label": "Stacked"
            },
            {
              "value": "columns",
              "label": "Two Columns"
            }
          ],
          "default": "stacked"
        },
        {
          "type": "header",
          "content": "Title Typography"
        },
        {
          "type": "select",
          "id": "title_font_family",
          "label": "Title Font Family",
          "options": [
            {
              "value": "heading",
              "label": "Heading Font"
            },
            {
              "value": "body",
              "label": "Body Font"
            }
          ],
          "default": "heading"
        },
        {
          "type": "range",
          "id": "title_font_weight",
          "label": "Title Font Weight",
          "min": 300,
          "max": 900,
          "step": 100,
          "default": 700
        },
        {
          "type": "select",
          "id": "title_font_size",
          "label": "Title Font Size",
          "options": [
            {
              "value": "text-sm",
              "label": "Small"
            },
            {
              "value": "text-base",
              "label": "Medium"
            },
            {
              "value": "text-lg",
              "label": "Large"
            },
            {
              "value": "text-xl",
              "label": "X-Large"
            }
          ],
          "default": "text-lg"
        },
        {
          "type": "header",
          "content": "Content Typography"
        },
        {
          "type": "select",
          "id": "content_font_family",
          "label": "Content Font Family",
          "options": [
            {
              "value": "heading",
              "label": "Heading Font"
            },
            {
              "value": "body",
              "label": "Body Font"
            }
          ],
          "default": "body"
        },
        {
          "type": "range",
          "id": "content_font_weight",
          "label": "Content Font Weight",
          "min": 300,
          "max": 900,
          "step": 100,
          "default": 400
        },
        {
          "type": "select",
          "id": "content_font_size",
          "label": "Content Font Size",
          "options": [
            {
              "value": "text-sm",
              "label": "Small"
            },
            {
              "value": "text-base",
              "label": "Medium"
            },
            {
              "value": "text-lg",
              "label": "Large"
            }
          ],
          "default": "text-base"
        },
        {
          "type": "header",
          "content": "Colors & Style"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#f3f4f6"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#111827"
        },
        {
          "type": "color",
          "id": "content_color",
          "label": "Content Color",
          "default": "#374151"
        },
        {
          "type": "select",
          "id": "border_radius",
          "label": "Border Radius",
          "options": [
            {
              "value": "rounded-none",
              "label": "None"
            },
            {
              "value": "rounded",
              "label": "Small"
            },
            {
              "value": "rounded-lg",
              "label": "Medium"
            },
            {
              "value": "rounded-xl",
              "label": "Large"
            }
          ],
          "default": "rounded-lg"
        },
        {
          "type": "header",
          "content": "Offer 1"
        },
        {
          "type": "text",
          "id": "title_1",
          "label": "Title",
          "default": "First Offer"
        },
        {
          "type": "richtext",
          "id": "content_1",
          "label": "Content"
        },
        {
          "type": "header",
          "content": "Offer 2"
        },
        {
          "type": "text",
          "id": "title_2",
          "label": "Title",
          "default": "Second Offer"
        },
        {
          "type": "richtext",
          "id": "content_2",
          "label": "Content"
        },
        {
          "type": "header",
          "content": "Offer 3"
        },
        {
          "type": "text",
          "id": "title_3",
          "label": "Title",
          "default": "Third Offer"
        },
        {
          "type": "richtext",
          "id": "content_3",
          "label": "Content"
        }
      ]
    },
    {
      "type": "share_buttons",
      "name": "Share buttons",
      "settings": [
        {
          "type": "paragraph",
          "content": "To improve user experience and performance, native share buttons are used when supported."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ],
          "default": "start"
        }
      ]
    },
    {
      "type": "benefits",
      "name": "Benefits List",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Benefits"
        },
        {
          "type": "header",
          "content": "Benefit 1"
        },
        {
          "type": "image_picker",
          "id": "icon_1",
          "label": "Icon 1"
        },
        {
          "type": "richtext",
          "id": "text_1",
          "label": "Text 1",
          "default": "<p>Skin, Hair</p>"
        },
        {
          "type": "header",
          "content": "Benefit 2"
        },
        {
          "type": "image_picker",
          "id": "icon_2",
          "label": "Icon 2"
        },
        {
          "type": "richtext",
          "id": "text_2",
          "label": "Text 2",
          "default": "<p>Muscle Recovery</p>"
        },
        {
          "type": "header",
          "content": "Benefit 3"
        },
        {
          "type": "image_picker",
          "id": "icon_3",
          "label": "Icon 3"
        },
        {
          "type": "richtext",
          "id": "text_3",
          "label": "Text 3",
          "default": "<p>No crash</p>"
        },
        {
          "type": "header",
          "content": "Benefit 4"
        },
        {
          "type": "image_picker",
          "id": "icon_4",
          "label": "Icon 4"
        },
        {
          "type": "richtext",
          "id": "text_4",
          "label": "Text 4",
          "default": "<p>Bloat-Free Digestion</p>"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_fixed_add_to_cart",
      "label": "Show add to cart on scroll",
      "default": true
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "range",
      "id": "desktop_media_width",
      "label": "Desktop media size",
      "min": 35,
      "max": 65,
      "step": 5,
      "unit": "%",
      "default": 55
    },
    {
      "type": "select",
      "id": "desktop_media_layout",
      "label": "Desktop media layout",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "grid_highlight",
          "label": "Grid with main media"
        },
        {
          "value": "carousel_thumbnails_left",
          "label": "Thumbnails left (carousel)"
        },
        {
          "value": "carousel_thumbnails_bottom",
          "label": "Thumbnails bottom (carousel)"
        }
      ],
      "default": "carousel_thumbnails_left"
    },
    {
      "type":"select",
      "id": "mobile_media_size",
      "label": "Mobile media size",
      "options": [
        {
          "value": "expanded",
          "label": "Expanded"
        },
        {
          "value": "contained",
          "label": "Contained"
        }
      ],
      "default": "expanded"
    },
    {
      "type": "select",
      "id": "mobile_carousel_control",
      "label": "Mobile carousel control",
      "options": [
        {
          "value": "dots",
          "label": "Dots"
        },
        {
          "value": "floating_dots",
          "label": "Floating dots"
        },
        {
          "value": "thumbnails",
          "label": "Thumbnails"
        },
        {
          "value": "free_scroll",
          "label": "Free scroll"
        }
      ],
      "default": "floating_dots"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay. Grid mode on desktop turn off autoplay.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": true
    },
    {
      "type": "header",
      "content": "Image zoom"
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable",
      "default": true
    },
    {
      "type": "range",
      "id": "max_image_zoom_level",
      "min": 1,
      "max": 4,
      "step": 0.5,
      "label": "Max zoom level",
      "default": 3
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Input colors",
      "info": "Applies to elements like quantity selector and variant selectors."
    },
    {
      "type": "color",
      "id": "input_background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "input_text_color",
      "label": "Text"
    }
  ]
}
{% endschema %}
