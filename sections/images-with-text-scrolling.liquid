{%- render 'section-spacing-collapsing' -%}

<style>
  #shopify-section-{{ section.id }} {
    --images-scrolling-block-count: {{ section.blocks.size }};
    --images-scrolling-image-ratio: {{ section.blocks.first.settings.image.aspect_ratio | default: 1 }};
  }

  @media screen and (max-width: 740px) {
    #shopify-section-{{ section.id }} {
      --images-scrolling-grid: {% if section.settings.stack_on_mobile %}none{% else %}auto / auto-flow 73vw{% endif %};
    }
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      --images-scrolling-grid-template-columns: {% if section.settings.image_position == 'start' %}[media] minmax(0, 1fr) [content] minmax(0, 0.8fr){% else %}[content] minmax(0, 0.8fr) [media] minmax(0, 1fr){% endif %};
    }
  }

  .star-rating {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
  }

  .star-rating__wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
  }

  .star-rating__stars-wrapper {
    display: flex;
    align-items: center;
  }

  .star-rating__stars {
    display: flex;
    gap: 4px;
    line-height: 0;
  }

  .star-rating__text-wrapper {
    display: flex;
    align-items: center;
    height: 24px;
  }

  .star-rating__text {
    font-size: 1.1em;
    font-weight: 500;
    line-height: 1;
    display: flex;
    align-items: center;
  }
</style>

<div {% render 'section-properties', narrow: true %}>
  {%- comment -%}
  ----------------------------------------------------------------------------------------------------------------------
  MOBILE VARIATION
  ----------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}
  <images-with-text-scrolling class="images-scrolling-mobile scroll-area bleed sm:unbleed">
    {%- for block in section.blocks -%}
      <div class="images-scrolling-mobile__item snap-start" {{ block.shopify_attributes }}>
        {%- if block.settings.image != blank -%}
          {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 100vw, 650px', widths: '200,300,400,600,800,1000,1200,1400,1600', class: 'rounded-sm' -}}
        {%- else -%}
          {%- capture image_placeholder -%}product-{%- cycle '1', '2', '3', '4' -%}{%- endcapture -%}
          {{- image_placeholder | placeholder_svg_tag: 'placeholder' -}}
        {%- endif -%}

        <div class="images-scrolling__content">
          {%- if section.settings.show_counter -%}
            <span class="images-scrolling__counter bold">{{ forloop.index | prepend: '00' | slice: -2, 2 }}</span>
          {%- endif -%}

          <div class="prose">
            {%- if block.settings.star_count > 0 -%}
              <div class="star-rating">
                <div class="star-rating__wrapper">
                  <div class="star-rating__stars-wrapper">
                    <div class="star-rating__stars">
                      {%- for i in (1..block.settings.star_count) -%}
                        {%- render 'icon' with 'rating-star', width: 24, height: 24 -%}
                      {%- endfor -%}
                    </div>
                  </div>
                  {%- if section.settings.rating_text != blank -%}
                    <div class="star-rating__text-wrapper">
                      <span class="star-rating__text">{{ section.settings.rating_text }}</span>
                    </div>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}

            {%- if block.settings.title != blank -%}
              <p class="h1" {% if settings.heading_apparition != 'none' %}reveal-on-scroll="true"{% endif %}>
                {%- render 'styled-text', content: block.settings.title, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient, apparition_effect: true -%}
              </p>
            {%- endif -%}

            {%- if block.settings.button_text != blank and block.settings.button_url != blank -%}
              {%- render 'button', content: block.settings.button_text, href: block.settings.button_url, style: block.settings.button_style, size: 'xl', class: 'mt-6' -%}
            {%- endif -%}

            {{- block.settings.content -}}
          </div>
        </div>
      </div>
    {%- endfor -%}
  </images-with-text-scrolling>

  {%- comment -%}
  ----------------------------------------------------------------------------------------------------------------------
  DESKTOP VARIATION
  ----------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}
  <images-with-text-scrolling scrolling-experience="{{ section.settings.desktop_image_effect }}" class="images-scrolling-desktop">
    <div class="images-scrolling-desktop__content-list">
      {%- for block in section.blocks -%}
        <div class="images-scrolling__content {% unless forloop.first %}opacity-0{% endunless %}">
          {%- if section.settings.show_counter -%}
            <span class="images-scrolling__counter bold">{{ forloop.index | prepend: '00' | slice: -2, 2 }}</span>
          {%- endif -%}

          <div class="prose">
            {%- if block.settings.star_count > 0 -%}
              <div class="star-rating">
                <div class="star-rating__wrapper">
                  <div class="star-rating__stars-wrapper">
                    <div class="star-rating__stars">
                      {%- for i in (1..block.settings.star_count) -%}
                        {%- render 'icon' with 'rating-star', width: 24, height: 24 -%}
                      {%- endfor -%}
                    </div>
                  </div>
                  {%- if section.settings.rating_text != blank -%}
                    <div class="star-rating__text-wrapper">
                      <span class="star-rating__text">{{ section.settings.rating_text }}</span>
                    </div>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}

            {%- if block.settings.title != blank -%}
              <p class="h1" {% if settings.heading_apparition != 'none' %}reveal-on-scroll="true"{% endif %}>
                {%- render 'styled-text', content: block.settings.title, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient, apparition_effect: true -%}
              </p>
            {%- endif -%}

            {%- if block.settings.button_text != blank and block.settings.button_url != blank -%}
              {%- render 'button', content: block.settings.button_text, href: block.settings.button_url, style: block.settings.button_style, size: 'xl', class: 'mt-6' -%}
            {%- endif -%}

            {{- block.settings.content -}}
          </div>
        </div>
      {%- endfor -%}
    </div>

    <div class="images-scrolling-desktop__media-wrapper">
      {%- for block in section.blocks -%}
        {%- if block.settings.image != blank -%}
          {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 100vw, 650px', widths: '200,300,400,600,800,1000,1200,1400,1600', class: 'rounded-sm' -}}
        {%- else -%}
          {%- capture image_placeholder -%}product-{%- cycle '1', '2', '3', '4' -%}{%- endcapture -%}
          {{- image_placeholder | placeholder_svg_tag: 'placeholder' -}}
        {%- endif -%}
      {%- endfor -%}
    </div>
  </images-with-text-scrolling>
</div>

{% schema %}
{
  "name": "Images and text scrolling",
  "tag": "section",
  "class": "shopify-section--images-and-text-scrolling",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 15,
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_on_mobile",
      "label": "Stack on mobile",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_counter",
      "label": "Show counter",
      "default": true
    },
    {
      "type": "select",
      "id": "desktop_image_effect",
      "label": "Desktop image transition",
      "options": [
        {
          "value": "reveal",
          "label": "Reveal"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "reveal"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Image position",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "end"
    },
    {
      "type": "header",
      "content": "Rating"
    },
    {
      "type": "text",
      "id": "rating_text",
      "label": "Rating text",
      "default": "1000+ QUENCHED"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1200 x 1600px .jpg recommended"
        },
        {
          "type": "range",
          "id": "star_count",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Number of stars",
          "default": 5
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "URL"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "options": [
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "secondary",
              "label": "Secondary"
            }
          ],
          "default": "primary"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Images and text scrolling",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "title": "Heading 1",
            "star_count": 5
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Heading 2"
          }
        }
      ]
    }
  ]
}
{% endschema %}
