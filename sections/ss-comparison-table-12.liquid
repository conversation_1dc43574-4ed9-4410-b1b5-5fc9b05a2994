{% comment %}
---------------------------------------------------------
Copyright © 2023 Section Store. All rights reserved.
Unauthorized copying, modification, distribution, or use
of this code or any portion of it, is strictly prohibited.
Violators will be prosecuted to the fullest extent of the law.
For inquiries or permissions, contact <EMAIL>
---------------------------------------------------------
{% endcomment %}

{%- liquid
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign margin_horizontal_mobile = section.settings.margin_horizontal_mobile
  assign margin_horizontal = section.settings.margin_horizontal
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy
  assign section_radius = section.settings.section_radius

  assign content_align = section.settings.content_align
  assign content_align_mobile = section.settings.content_align_mobile

  assign heading = section.settings.heading
  assign heading_custom = section.settings.heading_custom
  assign heading_font = section.settings.heading_font
  assign heading_size = section.settings.heading_size
  assign heading_size_mobile = section.settings.heading_size_mobile
  assign heading_height = section.settings.heading_height
  assign heading_color = section.settings.heading_color

  assign text = section.settings.text
  assign text_custom = section.settings.text_custom
  assign text_font = section.settings.text_font
  assign text_size = section.settings.text_size
  assign text_size_mobile = section.settings.text_size_mobile
  assign text_height = section.settings.text_height
  assign text_color = section.settings.text_color
  assign text_mt = section.settings.text_mt
  assign text_mt_mobile = section.settings.text_mt_mobile

  assign table_columns = section.settings.table_columns
  assign table_column_bg_color = section.settings.table_column_bg_color
  assign table_column_active_bg_color = section.settings.table_column_active_bg_color
  assign table_radius = section.settings.table_radius
  assign table_mt = section.settings.table_mt
  assign table_mt_mobile = section.settings.table_mt_mobile

  assign first_heading = section.settings.first_heading
  assign first_heading_image = section.settings.first_heading_image
  assign first_heading_url = section.settings.first_heading_url
  assign second_heading = section.settings.second_heading
  assign second_heading_image = section.settings.second_heading_image
  assign second_heading_url = section.settings.second_heading_url
  assign third_heading = section.settings.third_heading
  assign third_heading_image = section.settings.third_heading_image
  assign third_heading_url = section.settings.third_heading_url
  assign four_heading = section.settings.four_heading
  assign four_heading_image = section.settings.four_heading_image
  assign four_heading_url = section.settings.four_heading_url
  assign fives_heading = section.settings.fives_heading
  assign fives_heading_image = section.settings.fives_heading_image
  assign fives_heading_url = section.settings.fives_heading_url
  assign sixth_heading = section.settings.sixth_heading
  assign sixth_heading_image = section.settings.sixth_heading_image
  assign sixth_heading_url = section.settings.sixth_heading_url

  assign heading_image_size_mobile = section.settings.heading_image_size_mobile
  assign heading_image_size = section.settings.heading_image_size
  assign heading_image_border_thickness = section.settings.heading_image_border_thickness
  assign heading_image_border_color = section.settings.heading_image_border_color
  assign heading_image_radius = section.settings.heading_image_radius

  assign column_heading_size = section.settings.column_heading_size
  assign column_heading_size_mobile = section.settings.column_heading_size_mobile
  assign column_heading_color = section.settings.column_heading_color
  assign column_heading_active_color = section.settings.column_heading_active_color
  assign column_heading_custom = section.settings.column_heading_custom
  assign column_heading_font = section.settings.column_heading_font
  assign column_heading_height = section.settings.column_heading_height
  assign column_heading_align = section.settings.column_heading_align
  assign column_heading_align_mobile = section.settings.column_heading_align_mobile
  assign column_heading_padding_horizontal = section.settings.column_heading_padding_horizontal
  assign column_heading_padding_horizontal_mobile = section.settings.column_heading_padding_horizontal_mobile
  assign column_heading_padding_vertical = section.settings.column_heading_padding_vertical
  assign column_heading_padding_vertical_mobile = section.settings.column_heading_padding_vertical_mobile

  assign row_padding_horizontal = section.settings.row_padding_horizontal
  assign row_padding_horizontal_mobile = section.settings.row_padding_horizontal_mobile
  assign row_padding_vertical = section.settings.row_padding_vertical
  assign row_padding_vertical_mobile = section.settings.row_padding_vertical_mobile
  assign row_border_thickness = section.settings.row_border_thickness
  assign row_border_color = section.settings.row_border_color

  assign row_heading_size = section.settings.row_heading_size
  assign row_heading_size_mobile = section.settings.row_heading_size_mobile
  assign row_heading_color = section.settings.row_heading_color
  assign row_heading_custom = section.settings.row_heading_custom
  assign row_heading_font = section.settings.row_heading_font
  assign row_heading_height = section.settings.row_heading_height

  assign value_size = section.settings.value_size
  assign value_size_mobile = section.settings.value_size_mobile
  assign value_color = section.settings.value_color
  assign value_active_color = section.settings.value_active_color
  assign value_custom = section.settings.value_custom
  assign value_font = section.settings.value_font
  assign value_height = section.settings.value_height

  assign value_icon_size = section.settings.value_icon_size
  assign value_icon_size_mobile = section.settings.value_icon_size_mobile
  assign first_column_icon_size = section.settings.first_column_icon_size
  assign first_column_icon_size_mobile = section.settings.first_column_icon_size_mobile
  assign second_column_icon_size = section.settings.second_column_icon_size
  assign second_column_icon_size_mobile = section.settings.second_column_icon_size_mobile
  assign third_column_icon_size = section.settings.third_column_icon_size
  assign third_column_icon_size_mobile = section.settings.third_column_icon_size_mobile
  assign value_icon_color = section.settings.value_icon_color
  assign value_icon_active_color = section.settings.value_icon_active_color

  assign versus_icon_size = section.settings.versus_icon_size
  assign versus_icon_size_mobile = section.settings.versus_icon_size_mobile
  assign versus_icon_bg_color = section.settings.versus_icon_bg_color
  assign versus_vs_bg_color = section.settings.versus_vs_bg_color
  assign versus_value_icon_color = section.settings.versus_value_icon_color

  assign bottom_text = section.settings.bottom_text
  assign bottom_text_custom = section.settings.bottom_text_custom
  assign bottom_text_font = section.settings.bottom_text_font
  assign bottom_text_size = section.settings.bottom_text_size
  assign bottom_text_size_mobile = section.settings.bottom_text_size_mobile
  assign bottom_text_height = section.settings.bottom_text_height
  assign bottom_text_color = section.settings.bottom_text_color
  assign bottom_text_mt = section.settings.bottom_text_mt
  assign bottom_text_mt_mobile = section.settings.bottom_text_mt_mobile
  assign bottom_text_align = section.settings.bottom_text_align
  assign bottom_text_align_mobile = section.settings.bottom_text_align_mobile
-%}

{%- style -%}
  {{ heading_font | font_face: font_display: 'swap' }}
  {{ text_font | font_face: font_display: 'swap' }}
  {{ column_heading_font | font_face: font_display: 'swap' }}
  {{ row_heading_font | font_face: font_display: 'swap' }}
  {{ value_font | font_face: font_display: 'swap' }}
  {{ bottom_text_font | font_face: font_display: 'swap' }}

  .section-{{ section.id }} {
    border: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
    margin-left: {{ margin_horizontal_mobile }}rem;
    margin-right: {{ margin_horizontal_mobile }}rem;
    border-radius: {{ section_radius | times: 0.6 | round: 0 }}px;
    overflow: hidden;
  }

  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
  }


  .comparison-heading-{{ section.id }} {
    text-align: {{ content_align_mobile }};
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    font-style: var(--heading-font-style);
    letter-spacing: var(--heading-letter-spacing);
    text-transform: var(--heading-text-transform);
  }

  .comparison-heading-{{ section.id }} h2 {
    margin: 0;
    font-size: var(--text-h2);
    color: var(--heading-color);
    line-height: 1.1;
    text-transform: var(--heading-text-transform);
    text-decoration: none;
  }

  .comparison-text-{{ section.id }} {
    margin-top: {{ text_mt_mobile }}px;
    text-align: {{ content_align_mobile }};
  }

  .comparison-text-{{ section.id }} * {
    margin: 0;
    font-size: {{ text_size_mobile }}px;
    color: {{ text_color }};
    line-height: {{ text_height }}%;
    text-transform: unset;
    text-decoration: none;
  }

  .comparison-grid-{{ section.id }} {
    display: flex;
    width: 100%;
  }

  .comparison-table-wrapper-{{ section.id }} {
    overflow-x: hidden;
    scrollbar-width: none;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .comparison-table-wrapper-{{ section.id }}::-webkit-scrollbar {
    display: none;
  }

  .comparison-table-{{ section.id }} {
    min-width: auto !important;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding-bottom: 25px;
    padding-right: 0;
    margin-top: {{ table_mt_mobile }}px;
    position: relative;
  }

  @media(max-width: 767px) {
    .comparison-table-{{ section.id }} {
      max-width: 100% !important;
      width: 100%;
    }
  }

  .comparison-item-{{ section.id }} {
    position: relative;
    display: flex !important;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: left;
  }

  .comparison-item-{{ section.id }}:not(:first-child) {
    min-width: calc(25vw - 10px);
    max-width: calc(25vw - 10px);
    width: calc(25vw - 10px);
    justify-content: center;
  }

  .comparison-item-{{ section.id }}:first-child {
    flex-direction: row;
    gap: 10px;
    justify-content: flex-start;
    align-items: center;
    min-width: calc(50vw - 10px);
    max-width: calc(50vw - 10px);
    width: calc(50vw - 10px);
  }

  .comparison-grid-header-{{ section.id }} {
    padding-top: {{ versus_icon_size_mobile | divided_by: 4 }}px;
  }

  .comparison-grid-rows-{{ section.id }} {
    display: flex;
    position: relative;
    width: 100%;
  }

  .comparison-grid-rows-{{ section.id }}::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: {{ row_border_thickness }}px;
    background-color: {{ row_border_color }};
    z-index: 1;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} {
    padding: {{ column_heading_padding_vertical_mobile }}px 5px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:not(:first-child),
  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:not(:first-child) {
    background-color: {{ table_column_bg_color }};
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:not(:first-child):after {
    content: '';
    position: absolute;
    width: 100%;
    height: 25px;
    left: 0;
    bottom: -25px;
    background-color: {{ table_column_bg_color }};
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:last-child:after {
    border-bottom-right-radius: {{ table_radius }}px;
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:nth-child(3):after {
    border-bottom-left-radius: {{ table_radius }}px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}.active {
    background-color: {{ table_column_active_bg_color }};
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}.active {
    border: 0px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-column-heading-{{ section.id }} {
    margin: 10px 0 0 0;
    width: 100%;
    font-size: {{ column_heading_size_mobile }}px;
    color: {{ column_heading_color }};
    line-height: {{ column_heading_height }}%;
    text-transform: unset;
    text-align: {{ column_heading_align_mobile }};
    text-decoration: none;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}.active h3 {
    color: {{ column_heading_active_color }};
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }} {
    padding: {{ row_padding_vertical_mobile }}px 5px;
    display: flex;
    align-items: center;
    min-height: 45px;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child {
    padding-left: 8px;
    padding-right: 10px;
  }

  .comparison-grid-header-{{ section.id }} + .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child {
    border-top-left-radius: {{ table_radius }}px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(2) {
    border-top-left-radius: {{ table_radius }}px;
    {% if table_columns > 1 %}
    border-top-right-radius: {{ table_radius }}px;
    {% endif %}
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(3) {
    border-top-left-radius: {{ table_radius }}px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:last-child:before {
    content: '';
    position: absolute;
    width: 25px;
    height: 100%;
    right: -25px;
    border-top-right-radius: {{ table_radius }}px;
    background-color: {% if table_columns == 1 %}{{ table_column_active_bg_color }}{% else %}{{ table_column_bg_color }}{% endif %};
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:last-child:before {
    content: '';
    position: absolute;
    width: 25px;
    height: 100%;
    right: -25px;
    background-color: {% if table_columns == 1 %}{{ table_column_active_bg_color }}{% else %}{{ table_column_bg_color }}{% endif %};
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:nth-child(2):after {
    content: '';
    position: absolute;
    width: 100%;
    height: 25px;
    left: 0;
    bottom: -25px;
    border-bottom-right-radius: {{ table_radius }}px;
    border-bottom-left-radius: {{ table_radius }}px;
    background-color: {{ table_column_active_bg_color }};
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:last-child:after {
    width: calc(100% + 25px);
    border-bottom-right-radius: {{ table_radius }}px;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}.active {
    background-color: {{ table_column_active_bg_color }};
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:first-child:before {
    bottom: {{ column_heading_padding_vertical }}px
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:first-child {
    border: 0px;
  }

.comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child h3 {
  display: block;
  margin: 0;
  font-size: calc({{ row_heading_size_mobile }}px - 3px);
  color: {{ row_heading_color }};
  line-height: {{ row_heading_height }}%;
  text-align: left;
  font-weight: 400;
  font-family: 'Meltmino', 'Meltmino-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  letter-spacing: var(--heading-letter-spacing);
  text-transform: uppercase;
  word-break: normal;
  white-space: normal;
}

  .comparison-item-heading-image-{{ section.id }} {
    display: block;
    border: {{ heading_image_border_thickness }}px solid {{ heading_image_border_color }};
    border-radius: {{ heading_image_radius }}px;
    width: 100%;
    max-width: {{ heading_image_size_mobile }}px;
    overflow: hidden;
    aspect-ratio: 1/1;
  }

  .comparison-item-heading-image-{{ section.id }} img,
  .comparison-item-heading-image-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .comparison-item-heading-image-{{ section.id }} svg {
    background-color: #AFAFAF;
  }

  .comparison-value-{{ section.id }} {
    margin: 0;
    font-size: calc({{ value_size_mobile }}px - 1px);
    color: {{ value_color }};
    word-break: break-word;
    text-transform: unset;
    text-align: center;
  }

  .comparison-item-{{ section.id }}.active .comparison-value-{{ section.id }} {
    color: {{ value_active_color }};
  }

  .comparison-value-icon-{{ section.id }} {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .comparison-item-{{ section.id }}.active .comparison-value-icon-{{ section.id }} {
    width: 24px;
    height: 24px;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(3) .comparison-value-icon-{{ section.id }} {
    width: 24px;
    height: 24px;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(4) .comparison-value-icon-{{ section.id }} {
    width: 24px;
    height: 24px;
  }

  .comparison-value-icon-{{ section.id }} img,
  .comparison-value-icon-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
  }

  /* SVG styling is now handled with inline styles */

  .comparison-versus-icon-{{ section.id }} {
    position: absolute;
    top: 50%;
    right: calc(-{{ versus_icon_size_mobile | divided_by: 2 }}px - 2px);
    z-index: 2;
    transform: translateY(-50%);
    overflow: hidden;
    width: calc({{ versus_icon_size_mobile }}px - 4px);
    height: calc({{ versus_icon_size_mobile }}px - 4px);
  }

  .comparison-versus-icon-{{ section.id }} svg {
    display: block;
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .comparison-versus-icon-{{ section.id }} svg rect {
    fill: {{ versus_vs_bg_color }};
  }

  .comparison-versus-icon-{{ section.id }} svg path {
    fill: {{ versus_value_icon_color }};
  }

  .comparison-bottom-text-{{ section.id }} {
    margin-top: {{ bottom_text_mt_mobile }}px;
    text-align: {{ bottom_text_align_mobile }};
  }

  .comparison-bottom-text-{{ section.id }} * {
    margin: 0;
    font-size: {{ bottom_text_size_mobile }}px;
    color: {{ bottom_text_color }};
    line-height: {{ bottom_text_height }}%;
    text-transform: unset;
    text-decoration: none;
  }

  @media(min-width: 1024px) {

    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
      margin-left: {{ margin_horizontal }}rem;
      margin-right: {{ margin_horizontal }}rem;
      border-radius: {{ section_radius }}px;
    }

    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .comparison-heading-{{ section.id }} {
      text-align: {{ content_align }};
    }

    .comparison-heading-{{ section.id }} h2 {
      font-weight: var(--heading-font-weight);
      font-style: var(--heading-font-style);
      letter-spacing: var(--heading-letter-spacing);
      text-transform: var(--heading-text-transform);
    }

    .comparison-text-{{ section.id }} {
      margin-top: {{ text_mt }}px;
      text-align: {{ content_align }};
    }

    .comparison-text-{{ section.id }} * {
      font-size: {{ text_size }}px;
    }

  .comparison-table-wrapper-{{ section.id }} {
    margin-left: 0rem;
    margin-right: 0rem;
    padding-left: 0rem;
    padding-right: 0rem;
    overflow-x: hidden; /* Prevent horizontal overflow on desktop */
    display: flex;
    justify-content: center;
  }

    .comparison-table-{{ section.id }} {
      margin-top: {{ table_mt }}px;
      max-width: fit-content;
    }

    .comparison-item-{{ section.id }}:not(:first-child) {
      min-width: {{ section.settings.column_width }}px;
      max-width: {{ section.settings.column_width }}px;
      width: {{ section.settings.column_width }}px;
      justify-content: center;
    }

    .comparison-item-{{ section.id }}:first-child {
      min-width: {{ section.settings.row_heading_width }}px;
      max-width: {{ section.settings.row_heading_width }}px;
      width: {{ section.settings.row_heading_width }}px;
    }

    .comparison-item-heading-image-{{ section.id }} {
      max-width: {{ heading_image_size }}px;
    }

    .comparison-grid-header-{{ section.id }} {
      padding-top: {{ versus_icon_size | divided_by: 2 }}px;
    }

    .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} {
      padding: {{ column_heading_padding_vertical }}px {{ column_heading_padding_horizontal }}px;
    }

    .comparison-grid-header-{{ section.id }} .comparison-column-heading-{{ section.id }} {
      text-align: {{ column_heading_align }};
      font-size: {{ column_heading_size }}px;
    }

    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }} {
      padding: {{ row_padding_vertical }}px {{ row_padding_horizontal }}px;
      min-height: 70px;
    }

    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child h3 {
      font-size: {{ row_heading_size }}px;
      font-family: 'Meltmino', 'Meltmino-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      font-weight: 400;
      text-transform: uppercase;
    }

    .comparison-value-{{ section.id }} {
      font-size: {{ value_size }}px;
    }

    .comparison-value-icon-{{ section.id }}{
      width: {{ value_icon_size }}px;
      height: {{ value_icon_size }}px;
    }

    .comparison-item-{{ section.id }}.active .comparison-value-icon-{{ section.id }} {
      width: {{ first_column_icon_size }}px;
      height: {{ first_column_icon_size }}px;
    }

    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(3) .comparison-value-icon-{{ section.id }} {
      width: {{ second_column_icon_size }}px;
      height: {{ second_column_icon_size }}px;
    }

    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(4) .comparison-value-icon-{{ section.id }} {
      width: {{ third_column_icon_size }}px;
      height: {{ third_column_icon_size }}px;
    }

    .comparison-versus-icon-{{ section.id }} {
      right: calc(-{{ versus_icon_size | divided_by: 2 }}px - 5px);
      width: {{ versus_icon_size }}px;
      height: {{ versus_icon_size }}px;
    }

    .comparison-bottom-text-{{ section.id }} {
      margin-top: {{ bottom_text_mt }}px;
      text-align: {{ bottom_text_align }};
    }

    .comparison-bottom-text-{{ section.id }} * {
      font-size: {{ bottom_text_size }}px;
    }
  }

{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}px;
    }
  </style>
{% endunless %}

{% if heading_custom %}
  <style>
    .comparison-heading-{{ section.id }},
    .comparison-heading-{{ section.id }} h2 {
      font-family: {{ heading_font.family }}, {{ heading_font.fallback_families }};
      font-weight: {{ heading_font.weight }};
      font-style: {{ heading_font.style }};
    }
  </style>
{% endif %}

{% if text_custom %}
  <style>
    .comparison-text-{{ section.id }} * {
      font-family: {{ text_font.family }}, {{ text_font.fallback_families }};
      font-weight: {{ text_font.weight }};
      font-style: {{ text_font.style }};
    }
  </style>
{% endif %}

{% if column_heading_custom %}
  <style>
    .comparison-grid-header-{{ section.id }} .comparison-column-heading-{{ section.id }} {
      font-family: {{ column_heading_font.family }}, {{ column_heading_font.fallback_families }};
      font-weight: {{ column_heading_font.weight }};
      font-style: {{ column_heading_font.style }};
    }
  </style>
{% endif %}

{% if row_heading_custom %}
  <style>
    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child h3 {
      font-family: 'Meltmino', 'Meltmino-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      font-weight: 400;
      font-style: {{ row_heading_font.style }};
      text-transform: uppercase;
    }
  </style>
{% endif %}

{% if value_custom %}
  <style>
    .comparison-value-{{ section.id }} {
      font-family: {{ value_font.family }}, {{ value_font.fallback_families }};
      font-weight: {{ value_font.weight }};
      font-style: {{ value_font.style }};
    }
  </style>
{% endif %}

{% if bottom_text_custom %}
  <style>
    .comparison-bottom-text-{{ section.id }} * {
      font-family: {{ bottom_text_font.family }}, {{ bottom_text_font.fallback_families }};
      font-weight: {{ bottom_text_font.weight }};
      font-style: {{ bottom_text_font.style }};
    }
  </style>
{% endif %}

{% if table_columns > 1 %}
  <style>
    .comparison-grid-{{ section.id }} .comparison-item-{{ section.id }}.active {
      margin-right: 10px;
    }

    @media(min-width: 1024px) {
      .comparison-grid-{{ section.id }} .comparison-item-{{ section.id }}.active {
        margin-right: 20px;
      }
    }
  </style>
{% endif %}

<div class="section-{{ section.id }} comparison-{{ section.id }}" style="background-color:{{ background_color }}; background-image: {{ background_gradient }};">
  <div class="section-{{ section.id }}-settings">
    {% if heading != blank %}
      <div class="comparison-heading-{{ section.id }}">
        <h2>{{ heading }}</h2>
      </div>
    {% endif %}
    {% if text != blank %}
      <div class="comparison-text-{{ section.id }}">
        {{ text }}
      </div>
    {% endif %}
    <div class="comparison-table-wrapper-{{ section.id }}">
      <div class="comparison-table-{{ section.id }}">
        <div class="comparison-grid-{{ section.id }} comparison-grid-header-{{ section.id }}">
          <div class="comparison-item-{{ section.id }}"></div>
          {% if first_heading != blank %}
            <div class="comparison-item-{{ section.id }} active">
              {% if first_heading_image != blank %}
                <{% if first_heading_url != blank %}a href="{{ first_heading_url }}"{% else %}div{% endif %} class="comparison-item-heading-image-{{ section.id }}">
                  <img src="{{ first_heading_image | image_url }}" alt="{{ first_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                </{% if first_heading_url != blank %}a{% else %}div{% endif %}>
              {% endif %}
              <{% if first_heading_url != blank %}a href="{{ first_heading_url }}"{% else %}h3{% endif %} class="comparison-column-heading-{{ section.id }}">{{ first_heading }}</{% if first_heading_url != blank %}a{% else %}h3{% endif %}>
              {% if table_columns > 1 %}
                <div class="comparison-versus-icon-{{ section.id }}">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 86 83" height="83" width="86">
                  <rect fill="{{ versus_icon_bg_color }}" rx="41.2756" height="82.5513" width="85.5513"/>
                  <path fill="white" d="M18.824 26.8973H23.6086L31.5069 48.6177L39.4052 26.8973H43.9619L33.6334 54.2756H29.1526L18.824 26.8973ZM55.6546 54.5794C53.7306 54.5794 51.9712 54.1997 50.3764 53.4402C48.8068 52.6808 47.5411 51.5922 46.5791 50.1746C45.6424 48.7569 45.1108 47.1114 44.9842 45.2381H49.3511C49.427 46.2507 49.7435 47.1747 50.3004 48.0101C50.8827 48.8202 51.6421 49.4658 52.5788 49.9467C53.5154 50.4277 54.5407 50.6682 55.6546 50.6682C57.3 50.6682 58.6164 50.3138 59.6037 49.605C60.6163 48.8962 61.1226 47.9468 61.1226 46.757C61.1226 44.8584 59.9202 43.5547 57.5152 42.8458L52.8066 41.4788C50.7055 40.8966 49.0473 40.0359 47.8322 38.8967C46.6424 37.7322 46.0475 36.074 46.0475 33.9223C46.0475 32.5046 46.4399 31.2389 47.2246 30.125C48.0347 29.0111 49.1486 28.1504 50.5662 27.5428C51.9839 26.91 53.604 26.5935 55.4267 26.5935C58.2367 26.5935 60.5404 27.3023 62.3378 28.72C64.1351 30.1376 65.1224 32.0616 65.2996 34.4918H60.9328C60.7302 33.302 60.1353 32.3401 59.1481 31.6059C58.1861 30.8465 56.9456 30.4667 55.4267 30.4667C53.9078 30.4667 52.718 30.7705 51.8573 31.3781C50.9966 31.9856 50.5662 32.8211 50.5662 33.8843C50.5662 34.7703 50.8573 35.4918 51.4396 36.0487C52.0218 36.5803 52.8572 36.998 53.9458 37.3018L58.7304 38.6688C60.9075 39.2764 62.6036 40.1751 63.8187 41.3649C65.0338 42.5547 65.6414 44.3141 65.6414 46.6431C65.6414 48.2127 65.2237 49.605 64.3883 50.8201C63.5782 52.0099 62.4137 52.9339 60.8948 53.5921C59.4012 54.2503 57.6545 54.5794 55.6546 54.5794Z"/>
                  </svg>
                </div>
              {% endif %}
            </div>
          {% endif %}
          {% if table_columns > 1 and second_heading != blank %}
            <div class="comparison-item-{{ section.id }}">
              {% if second_heading_image != blank %}
                <{% if second_heading_url != blank %}a href="{{ second_heading_url }}"{% else %}div{% endif %} class="comparison-item-heading-image-{{ section.id }}">
                  <img src="{{ second_heading_image | image_url }}" alt="{{ second_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                </{% if second_heading_url != blank %}a{% else %}div{% endif %}>
              {% endif %}
              <{% if second_heading_url != blank %}a href="{{ second_heading_url }}"{% else %}h3{% endif %} class="comparison-column-heading-{{ section.id }}">{{ second_heading }}</{% if second_heading_url != blank %}a{% else %}h3{% endif %}>
            </div>
          {% endif %}
          {% if table_columns > 2 and third_heading != blank %}
            <div class="comparison-item-{{ section.id }}">
              {% if third_heading_image != blank %}
                <{% if third_heading_url != blank %}a href="{{ third_heading_url }}"{% else %}div{% endif %} class="comparison-item-heading-image-{{ section.id }}">
                  <img src="{{ third_heading_image | image_url }}" alt="{{ third_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                </{% if third_heading_url != blank %}a{% else %}div{% endif %}>
              {% endif %}
              <{% if third_heading_url != blank %}a href="{{ third_heading_url }}"{% else %}h3{% endif %} class="comparison-column-heading-{{ section.id }}">{{ third_heading }}</{% if third_heading_url != blank %}a{% else %}h3{% endif %}>
            </div>
          {% endif %}
          {% if table_columns > 3 and four_heading != blank %}
            <div class="comparison-item-{{ section.id }}">
              {% if four_heading_image != blank %}
                <{% if four_heading_url != blank %}a href="{{ four_heading_url }}"{% else %}div{% endif %} class="comparison-item-heading-image-{{ section.id }}">
                  <img src="{{ four_heading_image | image_url }}" alt="{{ four_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                </{% if four_heading_url != blank %}a{% else %}div{% endif %}>
              {% endif %}
              <{% if four_heading_url != blank %}a href="{{ four_heading_url }}"{% else %}h3{% endif %} class="comparison-column-heading-{{ section.id }}">{{ four_heading }}</{% if four_heading_url != blank %}a{% else %}h3{% endif %}>
            </div>
          {% endif %}
          {% if table_columns > 4 and fives_heading != blank %}
            <div class="comparison-item-{{ section.id }}">
              {% if fives_heading_image != blank %}
                <{% if fives_heading_url != blank %}a href="{{ fives_heading_url }}"{% else %}div{% endif %} class="comparison-item-heading-image-{{ section.id }}">
                  <img src="{{ fives_heading_image | image_url }}" alt="{{ fives_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                </{% if fives_heading_url != blank %}a{% else %}div{% endif %}>
              {% endif %}
              <{% if fives_heading_url != blank %}a href="{{ fives_heading_url }}"{% else %}h3{% endif %} class="comparison-column-heading-{{ section.id }}">{{ fives_heading }}</{% if fives_heading_url != blank %}a{% else %}h3{% endif %}>
            </div>
          {% endif %}
          {% if table_columns > 5 and sixth_heading != blank %}
            <div class="comparison-item-{{ section.id }}">
              {% if sixth_heading_image != blank %}
                <{% if sixth_heading_url != blank %}a href="{{ sixth_heading_url }}"{% else %}div{% endif %} class="comparison-item-heading-image-{{ section.id }}">
                  <img src="{{ sixth_heading_image | image_url }}" alt="{{ sixth_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                </{% if sixth_heading_url != blank %}a{% else %}div{% endif %}>
              {% endif %}
              <{% if sixth_heading_url != blank %}a href="{{ sixth_heading_url }}"{% else %}h3{% endif %} class="comparison-column-heading-{{ section.id }}">{{ sixth_heading }}</{% if sixth_heading_url != blank %}a{% else %}h3{% endif %}>
            </div>
          {% endif %}
        </div>
          {% for block in section.blocks %}
            <div class="comparison-grid-{{ section.id }} comparison-grid-rows-{{ section.id }}">
              <div class="comparison-item-{{ section.id }}">
                  <h3>{{ block.settings.row_heading }}</h3>
              </div>
              {% if first_heading != blank or first_heading_image != blank %}
                <div class="comparison-item-{{ section.id }} active">
                  {% if block.settings.first_column_image != blank or block.settings.first_column_icon != "none" %}
                    <div class="comparison-value-icon-{{ section.id }}">
                      {% if block.settings.first_column_image != blank %}
                        <img src="{{ block.settings.first_column_image | image_url }}" alt="{{ block.settings.first_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        {% if block.settings.first_column_icon == "check" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#2A2A2A" style="stroke: #2A2A2A; fill: #2A2A2A;"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#E7E8E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="stroke: #E7E8E5;"/>
                          </svg>
                        {% elsif block.settings.first_column_icon == "cross" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#E7E8E5" style="stroke: #2A2A2A; fill: #E7E8E5;"/>
                          </svg>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                  {% if block.settings.first_column != blank %}
                    <p class="comparison-value-{{ section.id }}">{{ block.settings.first_column }}</p>
                  {% endif %}
                </div>
              {% endif %}
              {% if table_columns > 1 and second_heading != blank or second_heading_image != blank %}
                <div class="comparison-item-{{ section.id }}">
                  {% if block.settings.second_column_image != blank or block.settings.second_column_icon != "none" %}
                    <div class="comparison-value-icon-{{ section.id }}">
                      {% if block.settings.second_column_image != blank %}
                        <img src="{{ block.settings.second_column_image | image_url }}" alt="{{ block.settings.second_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        {% if block.settings.second_column_icon == "check" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#2A2A2A" style="stroke: #2A2A2A; fill: #2A2A2A;"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#E7E8E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="stroke: #E7E8E5;"/>
                          </svg>
                        {% elsif block.settings.second_column_icon == "cross" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#E7E8E5" style="stroke: #2A2A2A; fill: #E7E8E5;"/>
                          </svg>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                  {% if block.settings.second_column != blank %}
                    <p class="comparison-value-{{ section.id }}">{{ block.settings.second_column }}</p>
                  {% endif %}
                </div>
              {% endif %}
              {% if table_columns > 2 and third_heading != blank or third_heading_image != blank %}
                <div class="comparison-item-{{ section.id }}">
                  {% if block.settings.third_column_image != blank or block.settings.third_column_icon != "none" %}
                    <div class="comparison-value-icon-{{ section.id }}">
                      {% if block.settings.third_column_image != blank %}
                        <img src="{{ block.settings.third_column_image | image_url }}" alt="{{ block.settings.third_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        {% if block.settings.third_column_icon == "check" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#2A2A2A" style="stroke: #2A2A2A; fill: #2A2A2A;"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="stroke: #ffffff;"/>
                          </svg>
                        {% elsif block.settings.third_column_icon == "cross" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#E7E8E5" style="stroke: #2A2A2A; fill: #E7E8E5;"/>
                          </svg>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                  {% if block.settings.third_column != blank %}
                    <p class="comparison-value-{{ section.id }}">{{ block.settings.third_column }}</p>
                  {% endif %}
                </div>
              {% endif %}
              {% if table_columns > 3 and four_heading != blank or four_heading_image != blank %}
                <div class="comparison-item-{{ section.id }}">
                  {% if block.settings.four_column_image != blank or block.settings.four_column_icon != "none" %}
                    <div class="comparison-value-icon-{{ section.id }}">
                      {% if block.settings.four_column_image != blank %}
                        <img src="{{ block.settings.four_column_image | image_url }}" alt="{{ block.settings.four_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        {% if block.settings.four_column_icon == "check" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#2A2A2A" style="stroke: #2A2A2A; fill: #2A2A2A;"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="stroke: #ffffff;"/>
                          </svg>
                        {% elsif block.settings.four_column_icon == "cross" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#E7E8E5" style="stroke: #2A2A2A; fill: #E7E8E5;"/>
                          </svg>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                  {% if block.settings.four_column != blank %}
                    <p class="comparison-value-{{ section.id }}">{{ block.settings.four_column }}</p>
                  {% endif %}
                </div>
              {% endif %}
              {% if table_columns > 4 and fives_heading != blank or fives_heading_image != blank %}
                <div class="comparison-item-{{ section.id }}">
                  {% if block.settings.fives_column_image != blank or block.settings.fives_column_icon != "none" %}
                    <div class="comparison-value-icon-{{ section.id }}">
                      {% if block.settings.fives_column_image != blank %}
                        <img src="{{ block.settings.fives_column_image | image_url }}" alt="{{ block.settings.fives_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        {% if block.settings.fives_column_icon == "check" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#2A2A2A" style="stroke: #2A2A2A; fill: #2A2A2A;"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="stroke: #ffffff;"/>
                          </svg>
                        {% elsif block.settings.fives_column_icon == "cross" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#E7E8E5" style="stroke: #2A2A2A; fill: #E7E8E5;"/>
                          </svg>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                  {% if block.settings.fives_column != blank %}
                    <p class="comparison-value-{{ section.id }}">{{ block.settings.fives_column }}</p>
                  {% endif %}
                </div>
              {% endif %}
              {% if table_columns > 5 and sixth_heading != blank or sixth_heading_image != blank %}
                <div class="comparison-item-{{ section.id }}">
                  {% if block.settings.sixth_column_image != blank or block.settings.sixth_column_icon != "none" %}
                    <div class="comparison-value-icon-{{ section.id }}">
                      {% if block.settings.sixth_column_image != blank %}
                        <img src="{{ block.settings.sixth_column_image | image_url }}" alt="{{ block.settings.sixth_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        {% if block.settings.sixth_column_icon == "check" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#2A2A2A" style="stroke: #2A2A2A; fill: #2A2A2A;"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="stroke: #ffffff;"/>
                          </svg>
                        {% elsif block.settings.sixth_column_icon == "cross" %}
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" style="overflow: visible;">
                            <circle cx="12" cy="12" r="10" stroke="#2A2A2A" stroke-width="1.5" fill="#E7E8E5" style="stroke: #2A2A2A; fill: #E7E8E5;"/>
                          </svg>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                  {% if block.settings.sixth_column != blank %}
                    <p class="comparison-value-{{ section.id }}">{{ block.settings.sixth_column }}</p>
                  {% endif %}
                </div>
              {% endif %}
            </div>
          {% endfor %}
      </div>
    </div>
    {% if bottom_text != blank %}
      <div class="comparison-bottom-text-{{ section.id }}">
        {{ bottom_text }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
  {
    "name": "SS - Comparison table #12",
    "settings": [
      {
        "type": "header",
        "content": "Content settings"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "select",
        "id": "content_align_mobile",
        "label": "Alignment - mobile",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "header",
        "content": "Heading settings"
      },
      {
        "type": "richtext",
        "id": "heading",
        "label": "Heading",
        "default": "<p>Comparison table #12</p>"
      },
      {
        "type": "checkbox",
        "id": "heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "heading_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 52
      },
      {
        "type": "range",
        "id": "heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 28
      },
      {
        "type": "range",
        "id": "heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },
      {
        "type": "header",
        "content": "Text settings"
      },
      {
        "type": "richtext",
        "id": "text",
        "label": "Text",
        "default": "<p>Inform customers about your products and highlight your brand's unique features.</p>"
      },
      {
        "type": "checkbox",
        "id": "text_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "text_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "text_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 18
      },
      {
        "type": "range",
        "id": "text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 150
      },
      {
        "type": "range",
        "id": "text_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 16
      },
      {
        "type": "range",
        "id": "text_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 16
      },
      {
        "type": "header",
        "content": "Table settings"
      },
      {
        "type": "range",
        "id": "table_columns",
        "min": 1,
        "max": 6,
        "step": 1,
        "label": "Number of columns",
        "default": 3
      },
      {
        "type": "range",
        "id": "table_radius",
        "min": 0,
        "max": 50,
        "step": 2,
        "unit": "px",
        "label": "Roundness",
        "default": 20
      },
      {
        "type": "range",
        "id": "table_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 24
      },
      {
        "type": "range",
        "id": "table_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 12
      },
      {
        "type": "range",
        "id": "column_width",
        "min": 80,
        "max": 250,
        "step": 10,
        "unit": "px",
        "label": "Column width",
        "info": "Adjust to make columns thinner or wider (desktop)",
        "default": 220
      },
      {
        "type": "range",
        "id": "column_width_mobile",
        "min": 80,
        "max": 200,
        "step": 10,
        "unit": "px",
        "label": "Column width - mobile",
        "info": "Adjust to make columns thinner or wider (mobile)",
        "default": 120
      },
      {
        "type": "range",
        "id": "row_heading_width",
        "min": 100,
        "max": 300,
        "step": 10,
        "unit": "px",
        "label": "Row heading width",
        "info": "Width of the first column (row headings) on desktop",
        "default": 200
      },
      {
        "type": "range",
        "id": "row_heading_width_mobile",
        "min": 80,
        "max": 250,
        "step": 10,
        "unit": "px",
        "label": "Row heading width - mobile",
        "info": "Width of the first column (row headings) on mobile",
        "default": 150
      },
      {
        "type": "header",
        "content": "Column headings settings"
      },
      {
        "type": "text",
        "id": "first_heading",
        "label": "First heading",
        "default": "Our Brand"
      },
      {
        "type": "image_picker",
        "id": "first_heading_image",
        "label": "First heading image"
      },
      {
        "type": "url",
        "id": "first_heading_url",
        "label": "First heading URL"
      },
      {
        "type": "text",
        "id": "second_heading",
        "label": "Second heading",
        "default": "Other 1"
      },
      {
        "type": "image_picker",
        "id": "second_heading_image",
        "label": "Second heading image"
      },
      {
        "type": "url",
        "id": "second_heading_url",
        "label": "Second heading URL"
      },
      {
        "type": "text",
        "id": "third_heading",
        "label": "Third heading",
        "default":"Other 2"
      },
      {
        "type": "image_picker",
        "id": "third_heading_image",
        "label": "Third heading image"
      },
      {
        "type": "url",
        "id": "third_heading_url",
        "label": "Third heading URL"
      },
      {
        "type": "text",
        "id": "four_heading",
        "label": "Fourth heading"
      },
      {
        "type": "image_picker",
        "id": "four_heading_image",
        "label": "Fourth heading image"
      },
      {
        "type": "url",
        "id": "four_heading_url",
        "label": "Fourth heading URL"
      },
      {
        "type": "text",
        "id": "fives_heading",
        "label": "Fifth heading"
      },
      {
        "type": "image_picker",
        "id": "fives_heading_image",
        "label": "Fifth heading image"
      },
      {
        "type": "url",
        "id": "fives_heading_url",
        "label": "Fifth heading URL"
      },
      {
        "type": "text",
        "id": "sixth_heading",
        "label": "Sixth heading"
      },
      {
        "type": "image_picker",
        "id": "sixth_heading_image",
        "label": "Sixth heading image"
      },
      {
        "type": "url",
        "id": "sixth_heading_url",
        "label": "Sixth heading URL"
      },
      {
        "type": "header",
        "content": "Heading image settings"
      },
      {
        "type": "range",
        "id": "heading_image_size",
        "min": 20,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Max width",
        "default": 70
      },
      {
        "type": "range",
        "id": "heading_image_size_mobile",
        "min": 20,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Max width - mobile",
        "default": 70
      },
      {
        "type": "range",
        "id": "heading_image_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Roundness",
        "default": 100
      },
      {
        "type": "range",
        "id": "heading_image_border_thickness",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 1
      },
      {
        "type": "header",
        "content": "Column heading settings"
      },
      {
        "type": "checkbox",
        "id": "column_heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "column_heading_font",
        "label": "Font Family",
        "default": "assistant_n4"
      },
      {
        "type": "range",
        "id": "column_heading_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 24
      },
      {
        "type": "range",
        "id": "column_heading_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "column_heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },
      {
        "type": "select",
        "id": "column_heading_align",
        "label": "Alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "select",
        "id": "column_heading_align_mobile",
        "label": "Alignment - mobile",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "range",
        "id": "column_heading_padding_horizontal",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal",
        "default": 16
      },
      {
        "type": "range",
        "id": "column_heading_padding_horizontal_mobile",
        "min": 0,
        "max": 40,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal - mobile",
        "default": 4
      },
      {
        "type": "range",
        "id": "column_heading_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical",
        "default": 24
      },
      {
        "type": "range",
        "id": "column_heading_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical - mobile",
        "default": 20
      },
      {
        "type": "header",
        "content": "Row settings"
      },
      {
        "type": "range",
        "id": "row_padding_horizontal",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal",
        "default": 16
      },
      {
        "type": "range",
        "id": "row_padding_horizontal_mobile",
        "min": 0,
        "max": 40,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal - mobile",
        "default": 4
      },
      {
        "type": "range",
        "id": "row_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical",
        "default": 24
      },
      {
        "type": "range",
        "id": "row_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical - mobile",
        "default": 20
      },
      {
        "type": "range",
        "id": "row_border_thickness",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 1
      },
      {
        "type": "header",
        "content": "Row heading settings"
      },
      {
        "type": "checkbox",
        "id": "row_heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "row_heading_font",
        "label": "Font family",
        "default": "assistant_n4"
      },
      {
        "type": "range",
        "id": "row_heading_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 18
      },
      {
        "type": "range",
        "id": "row_heading_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 12
      },
      {
        "type": "range",
        "id": "row_heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },
      {
        "type": "header",
        "content": "Value settings"
      },
      {
        "type": "checkbox",
        "id": "value_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "value_font",
        "label": "Font family",
        "default": "assistant_n4"
      },
      {
        "type": "range",
        "id": "value_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 24
      },
      {
        "type": "range",
        "id": "value_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "value_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },
      {
        "type": "header",
        "content": "Value icon settings"
      },
      {
        "type": "range",
        "id": "value_icon_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Default icon size",
        "default": 28
      },
      {
        "type": "range",
        "id": "value_icon_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Default icon size - mobile",
        "default": 24
      },
      {
        "type": "range",
        "id": "first_column_icon_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "First column icon size",
        "info": "Overrides default icon size for the first column",
        "default": 28
      },
      {
        "type": "range",
        "id": "first_column_icon_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "First column icon size - mobile",
        "info": "Overrides default icon size for the first column on mobile",
        "default": 24
      },
      {
        "type": "range",
        "id": "second_column_icon_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Second column icon size",
        "info": "Overrides default icon size for the second column",
        "default": 22
      },
      {
        "type": "range",
        "id": "second_column_icon_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Second column icon size - mobile",
        "info": "Overrides default icon size for the second column on mobile",
        "default": 20
      },
      {
        "type": "range",
        "id": "third_column_icon_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Third column icon size",
        "info": "Overrides default icon size for the third column",
        "default": 28
      },
      {
        "type": "range",
        "id": "third_column_icon_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Third column icon size - mobile",
        "info": "Overrides default icon size for the third column on mobile",
        "default": 24
      },
      {
        "type": "header",
        "content": "Versus icon settings"
      },
      {
        "type": "range",
        "id": "versus_icon_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Size",
        "default": 60
      },
      {
        "type": "range",
        "id": "versus_icon_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Size - mobile",
        "default": 48
      },
      {
        "type": "header",
        "content": "Additional details settings"
      },
      {
        "type": "richtext",
        "id": "bottom_text",
        "label": "Text",
        "default": "<p>Include additional details</p>"
      },
      {
        "type": "checkbox",
        "id": "bottom_text_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "bottom_text_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "bottom_text_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 12
      },
      {
        "type": "range",
        "id": "bottom_text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 12
      },
      {
        "type": "range",
        "id": "bottom_text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 150
      },
      {
        "type": "range",
        "id": "bottom_text_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 24
      },
      {
        "type": "range",
        "id": "bottom_text_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 20
      },
      {
        "type": "select",
        "id": "bottom_text_align",
        "label": "Alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "select",
        "id": "bottom_text_align_mobile",
        "label": "Alignment - mobile",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "header",
        "content": "Content colors"
      },
      {
        "type": "color",
        "label": "Heading",
        "id": "heading_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Text",
        "id": "text_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Additional details",
        "id": "bottom_text_color",
        "default": "#afabab"
      },
      {
        "type": "header",
        "content": "Columns colors"
      },
      {
        "type": "color",
        "label": "Heading",
        "id": "column_heading_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Heading active",
        "id": "column_heading_active_color",
        "default": "#e0004d"
      },
      {
        "type": "color",
        "label": "Text",
        "id": "value_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Text active",
        "id": "value_active_color",
        "default": "#e0004d"
      },
      {
        "type": "color",
        "label": "Background",
        "id": "table_column_bg_color",
        "default": "#f9f9ff"
      },
      {
        "type": "color",
        "label": "Background active",
        "id": "table_column_active_bg_color",
        "default": "#ffe0e9"
      },
      {
        "type": "color",
        "id": "heading_image_border_color",
        "label": "Heading image border",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Row colors"
      },
      {
        "type": "color",
        "label": "Heading",
        "id": "row_heading_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "row_border_color",
        "default": "#d9d9d9"
      },
      {
        "type": "header",
        "content": "Value icon colors"
      },
      {
        "type": "color",
        "label": "Default",
        "id": "value_icon_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Active",
        "id": "value_icon_active_color",
        "default": "#e0004d"
      },
      {
        "type": "header",
        "content": "Versus icon colors"
      },
      {
        "type": "color",
        "label": "Icon",
        "id": "versus_value_icon_color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "label": "Background",
        "id": "versus_icon_bg_color",
        "default": "#6c6ab4"
      },
      {
        "type": "color",
        "label": "VS Background",
        "id": "versus_vs_bg_color",
        "info": "Background color of the VS text in the icon",
        "default": "#2A2A2A"
      },
      {
        "type": "header",
        "content": "Section colors"
      },
      {
        "type": "color",
        "label": "Section background",
        "id": "background_color",
        "default": "#ffffff"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Section background gradient"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section margin (outside)"
      },
      {
        "type": "range",
        "id": "margin_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin top",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin bottom",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Margin sides",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Margin sides mobile",
        "default": 0
      },
      {
        "type": "header",
        "content": "Section padding (inside)"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Padding top",
        "default": 36
      },
      {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Padding bottom",
         "default": 36
      },
      {
        "type": "range",
        "id": "padding_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Padding sides",
        "default": 5
      },
      {
        "type": "range",
        "id": "padding_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Padding sides mobile",
        "default": 1.5
      },
      {
        "type": "header",
        "content": "Section Settings"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "range",
        "id": "content_width",
        "min": 400,
        "max": 2000,
        "step": 100,
        "unit": "px",
        "label": "Section content width",
        "default": 1200
      },
      {
        "type": "range",
        "id": "border_thickness",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 0
      },
      {
        "type": "range",
        "id": "section_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Section roundness",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "lazy",
        "label": "Lazy load",
        "info": "Lazy load images for speed optimisation",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "table_row",
        "name": "Table row",
        "settings": [
          {
            "type": "text",
            "id": "row_heading",
            "label": "Row heading",
            "default": "Row heading"
          },
          {
            "type": "text",
            "id": "first_column",
            "label": "First column value"
          },
          {
            "type": "image_picker",
            "id": "first_column_image",
            "label": "First column image",
            "info":"Custom images replaces icons"
          },
          {
            "type": "select",
            "id": "first_column_icon",
            "label": "First column icon",
            "default": "check",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "second_column",
            "label": "Second column value"
          },
          {
            "type": "image_picker",
            "id": "second_column_image",
            "label": "Second column image"
          },
          {
            "type": "select",
            "id": "second_column_icon",
            "label": "Second column icon",
            "default": "cross",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "third_column",
            "label": "Third column value"
          },
          {
            "type": "image_picker",
            "id": "third_column_image",
            "label": "Third column image"
          },
          {
            "type": "select",
            "id": "third_column_icon",
            "label": "Third column icon",
            "default": "check",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "four_column",
            "label": "Fourth column value"
          },
          {
            "type": "image_picker",
            "id": "four_column_image",
            "label": "Fourth column image"
          },
          {
            "type": "select",
            "id": "four_column_icon",
            "label": "Fourth column icon",
            "default": "check",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "fives_column",
            "label": "Fifth column value"
          },
          {
            "type": "image_picker",
            "id": "fives_column_image",
            "label": "Fifth column image"
          },
          {
            "type": "select",
            "id": "fives_column_icon",
            "label": "Fifth column icon",
            "default": "cross",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "sixth_column",
            "label": "Sixth column value"
          },
          {
            "type": "image_picker",
            "id": "sixth_column_image",
            "label": "Sixth column image"
          },
          {
            "type": "select",
            "id": "sixth_column_icon",
            "label": "Sixth column icon",
            "default": "cross",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "SS - Comparison table #12",
        "blocks": [
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Feature 1"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Feature 2"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Feature 3"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Feature 4"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Price",
              "first_column_icon": "none",
              "first_column": "Only $350",
              "second_column_icon": "none",
              "second_column": "$10,000",
              "third_column_icon": "none",
              "third_column": "$4,500"
            }
          }
        ]
      }
    ]
  }
{% endschema %}
