.electrolyte-info {
  margin: 20px 0;
  width: 100%;
}

.electrolyte-grid {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
}

.electrolyte-item {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  background-color: #DDDDDD;
  border-radius: var(--rounded-button);
  padding: 12px;
  transition: transform 0.2s ease;
  min-width: 0; /* Prevent flex items from overflowing */
}

.electrolyte-item:hover {
  transform: translateY(-2px);
}

.electrolyte-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  color: #E7E8E5;
  flex-shrink: 0;
  text-align: center;
  line-height: 1;
  padding-top: 2px; /* Adjust vertical position */
  padding-left: 1px; /* Adjust horizontal position */
  font-family: var(--text-font-family);
  position: relative; /* For absolute positioning fallback */
}

/* Additional centering technique using absolute positioning */
.electrolyte-circle::before {
  content: attr(data-symbol);
  position: absolute;
  top: 52%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #E7E8E5;
  font-weight: bold;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.magnesium-circle,
.sodium-circle,
.potassium-circle {
  background-color: #2A2A2A;
}

.electrolyte-details {
  display: flex;
  flex-direction: column;
}

.electrolyte-name {
  font-weight: 600;
  font-size: var(--text-xs);
  line-height: 1.2;
}

@media screen and (max-width: 420px) {
  .electrolyte-name {
    font-size: 10px !important;
  }
}

.electrolyte-amount {
  font-weight: 700;
  font-size: var(--text-base);
  line-height: 1.2;
}

/* Media query for mobile */
@media screen and (max-width: 767px) {
  .electrolyte-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }

  .electrolyte-item {
    flex: 1;
    min-width: 100px;
    padding: 8px;
  }

  .electrolyte-circle {
    width: 32px;
    height: 32px;
    font-size: 14px;
    padding-top: 2px;
    padding-left: 1px;
  }

  .electrolyte-circle::before {
    font-size: 14px;
  }

  .electrolyte-name {
    font-size: 12px;
  }

  .electrolyte-amount {
    font-size: 14px;
  }
}

/* Media query for very small screens */
@media screen and (max-width: 359px) {
  .electrolyte-item {
    min-width: 90px;
    padding: 6px;
  }

  .electrolyte-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
    padding-top: 1px;
    padding-left: 1px;
  }

  .electrolyte-circle::before {
    font-size: 12px;
  }

  .electrolyte-name {
    font-size: 11px;
  }

  .electrolyte-amount {
    font-size: 13px;
  }
}
