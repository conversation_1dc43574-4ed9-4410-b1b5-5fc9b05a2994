/* Hide the redundant text in variant picker option info */
.variant-picker__option-info .h-stack span {
  display: none;
}

/* Style the option labels (Amount:, Flavor:, etc.) to match appstle_widget_title */
.variant-picker__option-info .h-stack legend.text-subdued {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-size: var(--text-h5);
  color: rgb(var(--text-color));
  text-transform: var(--heading-text-transform);
  letter-spacing: var(--heading-letter-spacing);
  line-height: 1.4;
  margin: 0;
}

/* Set background color for all block swatches to make them look selected */
.variant-picker__option-values .block-swatch {
  background-color: #DDDDDD;
}

/* Ensure the selected swatch still has a visible outline */
.variant-picker__option-values input:checked + .block-swatch:before {
  opacity: 1;
  transform: scale(1);
}

/* Make subscription prices display side by side instead of stacked */
.appstle_subscription_amount_wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
}

/* Place Amount and Flavor pickers side by side and full width */
.product-info variant-picker.variant-picker {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  gap: var(--spacing-4) !important;
  grid-template-columns: none !important;
  width: 100% !important;
  max-width: 100% !important;
}

.product-info variant-picker.variant-picker > fieldset {
  flex: 1 !important;
  min-width: 45% !important;
  margin-bottom: 0 !important;
}

/* Make variant option values take full width */
.product-info .variant-picker__option-values {
  width: 100% !important;
  max-width: 100% !important;
}

/* Make block swatches expand to fill available space and match purchase plan button border width */
.product-info .variant-picker__option-values .block-swatch {
  flex: 1 !important;
  width: 100% !important;
  justify-content: center !important;
  text-align: center !important;
  border-width: 2px !important;
  border-color: rgb(var(--text-color) / 0.5) !important;
  text-transform: uppercase; 
}

/* Remove any extra padding/margin that might affect alignment */
.product-info .variant-picker__option-values.scroll-area {
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure the variant picker container aligns with purchase plan buttons */
.product-info variant-picker.variant-picker {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Add consistent spacing before Purchase Plan section */
.product-info__block-item[data-block-type="buy-buttons"] {
  margin-top: 30px !important;
}

/* Ensure consistent spacing on mobile */
@media screen and (max-width: 767px) {
  .appstle_subscription_amount_wrapper {
    display: flex !important;
    flex-direction: row !important;
  }

  /* Keep side-by-side layout on mobile */
  .product-info variant-picker.variant-picker {
    display: flex !important;
    flex-direction: row !important;
    gap: var(--spacing-3) !important;
    grid-template-columns: none !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .product-info variant-picker.variant-picker > fieldset {
    flex: 1 !important;
    min-width: 45% !important;
    margin-bottom: 0 !important;
  }

  /* Ensure full width on mobile too */
  .product-info .variant-picker__option-values {
    width: 100% !important;
    max-width: 100% !important;
  }

  .product-info .variant-picker__option-values .block-swatch {
    flex: 1 !important;
    width: 100% !important;
    justify-content: center !important;
    text-align: center !important;
    border-width: 2px !important;
    border-color: rgb(var(--text-color) / 0.5) !important;
  }

  /* Remove any extra padding/margin on mobile */
  .product-info .variant-picker__option-values.scroll-area {
    padding: 0 !important;
    margin: 0 !important;
  }
}
