/* Custom styles for the cart drawer */

/* Increase border radius and remove transparent border */
.drawer::part(content) {
  border-radius: 16px 0 0 16px !important; /* Larger rounded corners on the left side only */
  border: none !important; /* Remove any border */
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1); /* Add subtle shadow for depth */
  overflow: hidden; /* Ensure content doesn't overflow the rounded corners */
}

/* Ensure the drawer slides seamlessly from the right */
.cart-drawer {
  padding-right: 0 !important; /* Remove right padding to make it flush with the edge */
  z-index: 1000 !important; /* High z-index but allow header to remain visible */
  max-width: 500px !important; /* Set maximum width to 500px */
  width: 500px !important; /* Set fixed width to 500px */
}

/* Improve overlay fade-in/out animation and add blur effect */
.cart-drawer::part(overlay) {
  opacity: 0;
  -webkit-backdrop-filter: blur(0px);
  backdrop-filter: blur(0px);
  background: rgba(0, 0, 0, 0.4) !important; /* Slightly darker background for better blur effect */
  transition: opacity 0.3s ease-out, backdrop-filter 0.3s ease-out, -webkit-backdrop-filter 0.3s ease-out !important;
  z-index: 999 !important; /* Ensure overlay appears behind drawer content but doesn't hide header */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

.cart-drawer[open]::part(overlay) {
  opacity: 1;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

/* Basic styles for the cart drawer - animation is now handled by JavaScript */
.cart-drawer::part(content) {
  z-index: 1000 !important; /* High z-index but allow header to remain visible */
  transform: translateX(100%) !important; /* Start position for the drawer (off-screen) */
  transition: transform 0.4s cubic-bezier(0.86, 0, 0.07, 1) !important; /* Smooth transition */
}

/* When drawer is open, slide it in */
.cart-drawer[open]::part(content) {
  transform: translateX(0) !important; /* End position for the drawer (on-screen) */
}

/* Remove any padding that might create a gap */
@media screen and (min-width: 700px) {
  .drawer {
    padding-right: 0 !important;
  }

  /* Ensure the drawer is flush with the right edge of the screen and has the correct width */
  .cart-drawer.drawer--lg {
    right: 0 !important;
    width: 500px !important; /* Override the drawer--lg width of 680px */
    max-width: 500px !important;
  }
}

/* Style for when drawer is open to prevent layout shifts */
body.drawer-open {
  overflow: hidden !important;
  position: relative;
  /* Ensure the header remains visible */
  height: 100%;
}

/* Fix for mobile view */
@media screen and (max-width: 699px) {
  .drawer::part(content) {
    border-radius: 16px 0 0 16px !important; /* Keep consistent border radius on mobile */
  }

  .cart-drawer {
    right: 0 !important;
    padding: 0 !important; /* Remove all padding on mobile */
    z-index: 1000 !important; /* High z-index but allow header to remain visible on mobile */
    width: 100% !important; /* Full width on mobile */
    max-width: 500px !important; /* But still capped at 500px */
  }

  /* Ensure the drawer content is flush with the edge */
  .cart-drawer .cart-drawer__inner {
    padding-right: 0 !important;
  }

  /* Ensure blur effect works well on mobile */
  .cart-drawer::part(overlay) {
    -webkit-backdrop-filter: blur(0px);
    backdrop-filter: blur(0px);
    background: rgba(0, 0, 0, 0.5) !important; /* Slightly darker on mobile for better visibility */
    z-index: 999 !important; /* Ensure overlay appears behind drawer content but doesn't hide header on mobile */
  }

  .cart-drawer[open]::part(overlay) {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
  }
}
